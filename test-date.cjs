const moment = require('moment');

// Test the current implementation (updated version)
function toLocal(date, inputFormat = 'yyyy-MM-DDTHH:mm:ss.SSS+0000', outputFormat = 'yyyy-MM-DDTHH:mm:ss') {
  if (!date) return '';
  // Handle both lowercase and uppercase format strings for compatibility
  const normalizedInputFormat = inputFormat.replace(/yyyy/g, 'YYYY').replace(/dd/g, 'DD');
  const normalizedOutputFormat = outputFormat.replace(/yyyy/g, 'YYYY').replace(/dd/g, 'DD');

  return moment.utc(date, normalizedInputFormat).local().format(normalizedOutputFormat);
}

console.log('=== Testing Date Conversion ===');
console.log('Current timezone offset:', moment().utcOffset(), 'minutes');

// Test case 1: Your exact UTC time
const utcTime = '2025-11-12T18:29:00';
console.log('\nInput UTC time:', utcTime);

// Test with different input formats
console.log('\n--- Testing different input formats ---');
console.log('1. Default format (yyyy-MM-DDTHH:mm:ss.SSS+0000):', toLocal(utcTime));
console.log('2. Matching format (yyyy-MM-DDTHH:mm:ss):', toLocal(utcTime, 'yyyy-MM-DDTHH:mm:ss'));
console.log('3. No format specified:', moment.utc(utcTime).local().format('yyyy-MM-DDTHH:mm:ss'));

// Test with moment's standard formats
console.log('\n--- Testing with moment standard formats ---');
console.log('4. YYYY-MM-DDTHH:mm:ss format:', toLocal(utcTime, 'YYYY-MM-DDTHH:mm:ss', 'YYYY-MM-DDTHH:mm:ss'));
console.log('5. ISO format:', moment.utc(utcTime).local().format('YYYY-MM-DDTHH:mm:ss'));

// Debug moment parsing
console.log('\n--- Debug moment parsing ---');
console.log('moment.utc(utcTime) as UTC:', moment.utc(utcTime).format());
console.log('moment.utc(utcTime).local():', moment.utc(utcTime).local().format());
console.log('Expected IST time should be around 23:59 on Nov 12th');

// Test the old method (what was causing the issue)
function toLocalOld(date, inputFormat = 'yyyy-MM-DDTHH:mm:ss.SSS+0000', outputFormat = 'yyyy-MM-DDTHH:mm:ss') {
  if (!date) return '';
  return moment(date, inputFormat).add(moment().utcOffset(), 'minutes').format(outputFormat);
}

console.log('\n--- Comparison with old method ---');
console.log('NEW method result:', toLocal(utcTime, 'yyyy-MM-DDTHH:mm:ss'));
console.log('OLD method result:', toLocalOld(utcTime, 'yyyy-MM-DDTHH:mm:ss'));

// Test edge case - what if the input has timezone info?
const utcTimeWithZ = '2025-11-12T18:29:00Z';
console.log('\n--- Testing with Z suffix ---');
console.log('Input with Z:', utcTimeWithZ);
console.log('NEW method with Z:', toLocal(utcTimeWithZ, 'yyyy-MM-DDTHH:mm:ss'));
console.log('moment.utc(utcTimeWithZ).local():', moment.utc(utcTimeWithZ).local().format('yyyy-MM-DDTHH:mm:ss'));

// Test with a time that would cross date boundary
const lateUtcTime = '2025-11-12T19:00:00';
console.log('\n--- Testing with time that crosses date boundary ---');
console.log('Input UTC (19:00):', lateUtcTime);
console.log('NEW method result:', toLocal(lateUtcTime, 'yyyy-MM-DDTHH:mm:ss'));
console.log('OLD method result:', toLocalOld(lateUtcTime, 'yyyy-MM-DDTHH:mm:ss'));
console.log('This should be 00:30 on Nov 13th in IST');

// Test with exact format from your codebase
console.log('\n--- Testing with exact codebase usage ---');
const testTime = '2025-11-12T18:29:00';
console.log('Using default inputFormat (yyyy-MM-DDTHH:mm:ss.SSS+0000):');
console.log('Result:', toLocal(testTime));
console.log('Using yyyy-MM-DDTHH:mm:ss inputFormat:');
console.log('Result:', toLocal(testTime, 'yyyy-MM-DDTHH:mm:ss'));

// Test format case sensitivity issue
console.log('\n--- Testing format case sensitivity ---');
console.log('lowercase yyyy format:', toLocal(testTime, 'yyyy-MM-DDTHH:mm:ss', 'yyyy-MM-DDTHH:mm:ss'));
console.log('uppercase YYYY format:', toLocal(testTime, 'YYYY-MM-DDTHH:mm:ss', 'YYYY-MM-DDTHH:mm:ss'));

// Test what happens if moment doesn't recognize the format
console.log('\n--- Testing moment format recognition ---');
console.log('moment.utc with yyyy format:', moment.utc(testTime, 'yyyy-MM-DDTHH:mm:ss').format());
console.log('moment.utc with YYYY format:', moment.utc(testTime, 'YYYY-MM-DDTHH:mm:ss').format());
console.log('moment.utc without format:', moment.utc(testTime).format());
