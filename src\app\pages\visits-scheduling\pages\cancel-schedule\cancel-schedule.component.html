<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">{{ isPassGenerated ? "Canceled " : "Cancel " }} Lesson</div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="onCloseSideNav()">
        Close
      </button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="!isPassGenerated ? onCancelSchedule() : onScheduleLesson()"
        [appLoader]="showBtnLoader">
        {{ isPassGenerated ? "Schedule Make-Up Lesson" : "Cancel Lesson" }}
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    @if (!isPassGenerated) {
      <form [formGroup]="cancelScheduleForm">
        <mat-form-field>
          <mat-label>Reason for Cancellation</mat-label>
          <textarea
            matInput
            formControlName="notes"
            cdkTextareaAutosize
            cdkAutosizeMinRows="3"
            cdkAutosizeMaxRows="10"></textarea>
          <mat-error>
            <app-error-messages [control]="cancelScheduleForm.controls.notes"></app-error-messages>
          </mat-error>
        </mat-form-field>
        @if(selectedScheduleDetails?.classType === classTypes.RECURRING && !isWithinNext24Hours(selectedScheduleDetails?.start!)) {
          @if (currentUser?.dependentDetails) {
            <mat-form-field>
              <mat-label>Select Student</mat-label>
              <mat-select formControlName="studentId" placeholder="Select Student" (selectionChange)="resetInstrumentSelection()">
                <mat-option *ngFor="let dependent of studentList" [value]="dependent.id" (click)="getStudentGrades(dependent.id!)">
                  {{ dependent.firstName }} {{ dependent.lastName }}
                </mat-option>
              </mat-select>
              <mat-error>
                <app-error-messages [control]="cancelScheduleForm.controls.studentId"></app-error-messages>
              </mat-error>
            </mat-form-field>
          }
          <mat-form-field>
            <mat-label>Select Instrument</mat-label>
            <mat-select formControlName="instrumentId" placeholder="Select Instrument">
              <mat-option *ngFor="let instrument of studentInstruments" [value]="instrument.studentGrade.instrumentId">
                {{ instrument.studentGrade.instrumentName }}
              </mat-option>
            </mat-select>
            <mat-error>
              <app-error-messages [control]="cancelScheduleForm.controls.instrumentId"></app-error-messages>
            </mat-error>
          </mat-form-field>
        }
      </form>
    } @else {
      <div class="lesson-cancel-wrapper">
        <div>
          <img [src]="constants.staticImages.icons.checkCircle" height="70" width="70" alt="" />
          <div class="success-title">Lesson Canceled Successfully</div>
          <div class="success-message">
            Your lesson has been <span>canceled successfully.</span> You will
            <span>receive a makeup lesson pass</span> since you cancel 24 hours in advance. This pass can be
            <span>used to schedule a future lesson</span> at your convenience.
          </div>
        </div>
      </div>
    }
  </div>
</div>
