<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-850"
    [disableClose]="true">
    @if (isSideNavOpen) {
      <app-view-attendance
        [selectedScheduleId]="selectedScheduleId"
        [currentUser$]="currentUser"
        (updateAttendance)="getSchedule($event)"
        (closeSideNav)="toggleSideNav(false, null)"></app-view-attendance>
    }
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="auth-page-wrapper auth-page-with-header">
      <div class="filter-and-count-wrapper">
        <div class="search-and-count-wrapper-auth">
          <div class="total-users">
            Total: <span>{{ totalCount }}</span>
          </div>
          <div class="search-bar">
            <mat-form-field class="search-bar-wrapper ms-3">
              <input
                matInput
                placeholder="Search.."
                [(ngModel)]="filters.searchTerm"
                (ngModelChange)="onSearchTermChanged()" />
              <mat-icon matTextPrefix>search</mat-icon>
            </mat-form-field>
          </div>
        </div>
        <div class="filter-wrapper">
          <div class="mat-start-date">
            <mat-form-field class="search-bar-wrapper">
              <mat-icon class="me-2" (click)="updateDay(-1)">keyboard_arrow_left</mat-icon>
              <input
                matInput
                [matDatepicker]="picker"
                (click)="picker.open()"
                [(ngModel)]="filters.minScheduleDate"
                (dateChange)="getSchedule()" />
              <mat-icon (click)="updateDay(1)">keyboard_arrow_right</mat-icon>
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>

          <mat-form-field class="search-bar-wrapper">
            <mat-select [(ngModel)]="filters.classType" (selectionChange)="getSchedule()">
              <mat-option [value]="all.ALL">All Class Type</mat-option>
              <mat-option *ngFor="let classType of constants.allClassTypeOptions" [value]="classType.value">
                {{ classType.label }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field class="search-bar-wrapper">
            <mat-select [(ngModel)]="filters.locationId" (selectionChange)="getSchedule()">
              <mat-option [value]="all.ALL">All Location</mat-option>
              <mat-option *ngFor="let location of locations" [value]="location.schoolLocations.id">
                {{ location.schoolLocations.locationName }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <div class="o-card">
        <div class="o-card-body">
          <div class="o-table">
            <div class="o-row o-header">
              <div class="o-cell first-cell">Lesson</div>
              <div class="o-cell">Class Type</div>
              <!-- <div class="o-cell">Room No.</div> -->
              <div class="o-cell">Date-Time</div>
              <div class="o-cell">Location</div>
              <div class="o-cell">Action</div>
            </div>
            <div class="dotted-divider"></div>
            <div class="content hide-pagination">
              <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : studentTemplate"></ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #studentTemplate>
  <ng-container [ngTemplateOutlet]="totalCount ? studentTableContent : noDataFound"></ng-container>
</ng-template>

<ng-template #studentTableContent>
  @for (scheduleDetail of filteredScheduleDetails; track $index) {
    @if (!scheduleDetail.isDraftSchedule) {
      <div class="o-row">
        <div class="o-cell first-cell instructor-name-photo-wrapper" [ngClass]="{ strike : scheduleDetail.isCancelSchedule }" (click)="toggleSideNav(true, scheduleDetail.id)">
          @if (scheduleDetail.isCancelSchedule) {
            @if (scheduleDetail.isLateCancelSchedule) {
              <div class="me-1">Late Canceled: </div>
            }
            @else {
              <div class="me-1">Canceled: </div>
            }
          }
          @switch (scheduleDetail.classType) {
            @case (classTypes.GROUP_CLASS) {
              <div>
                {{ scheduleDetail.groupClassName | titlecase }} ({{
                  getTimeDiff(scheduleDetail.start, scheduleDetail.end)
                }})
              </div>
            }
            @case (classTypes.ENSEMBLE_CLASS) {
              <div>
                {{ scheduleDetail.ensembleClassName | titlecase }} ({{ getTimeDiff(scheduleDetail.start, scheduleDetail.end) }})
              </div>
            }
            @case (classTypes.SUMMER_CAMP) {
              <div>
                {{ scheduleDetail.campName | titlecase }} ({{ getTimeDiff(scheduleDetail.start, scheduleDetail.end) }})
              </div>
            }
            @case (classTypes.INTRODUCTORY) {
              <div>
                Introductory {{ scheduleDetail.instrumentName }} Lesson ({{ getTimeDiff(scheduleDetail.start, scheduleDetail.end) }})
              </div>
            }
            @default {
              <div>
                {{ scheduleDetail.instrumentName }} Lesson ({{ getTimeDiff(scheduleDetail.start, scheduleDetail.end) }})
              </div>
            }
          }
        </div>
        <div class="o-cell text-gray">
          {{ schedulerService.getClassType(scheduleDetail.classType) }}
        </div>
        <!-- <div class="o-cell text-gray">
            {{ scheduleDetail.roomName | dashIfEmpty }}
          </div> -->
        <div class="o-cell text-gray">
          <div>{{ scheduleDetail.start | localDate | date: constants.fullDate }}</div>
          <div>
            {{ scheduleDetail.start | localDate | date: constants.dateFormats.hh_mm_a }} -
            {{ scheduleDetail.end | localDate | date: constants.dateFormats.hh_mm_a }}
          </div>
        </div>
        <div class="o-cell text-gray">
          <img [src]="constants.staticImages.icons.location" alt="" /> {{ scheduleDetail.locationName }}
        </div>
        <div class="o-cell text-gray">
          <button
            mat-raised-button
            color="accent"
            [ngClass]="{ status: scheduleDetail.isAllPresent }"
            class="mat-accent-btn back-btn"
            type="button"
            (click)="toggleSideNav(true, scheduleDetail.id)">
            {{
              currentUser?.userRoleId === constants.roleIds.ADMIN ||
              currentUser?.dependentId !== scheduleDetail?.instructorId
                ? "View "
                : "Take "
            }}
            Attendance
          </button>
        </div>
      </div>
    }
    @if ($index < filteredScheduleDetails.length - 1) {
      <div class="dotted-divider"></div>
    }
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-wrapper">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
