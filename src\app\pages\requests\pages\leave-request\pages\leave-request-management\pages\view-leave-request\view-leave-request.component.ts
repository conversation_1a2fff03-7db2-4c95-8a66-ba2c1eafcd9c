import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { LeaveApprovalForm, LeaveRequestDetails, LeaveStatus, LeaveType, SubstituteAvailabilityRequest } from '../../../../models';
import moment from 'moment';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { LeaveRequestService } from '../../../../services';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { MatSelectModule } from '@angular/material/select';
import { Instructor } from 'src/app/schedule-introductory-lesson/models';
import { CBGetResponse } from 'src/app/shared/models';
import { CommonUtils } from 'src/app/shared/utils';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { LocalStorageService, StorageItem } from 'src/app/shared/services/local-storage.service';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    MatButtonModule,
    SharedModule,
    MatTooltipModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatSelectModule
  ]
};

@Component({
  selector: 'app-view-leave-request',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './view-leave-request.component.html',
  styleUrl: './view-leave-request.component.scss'
})
export class ViewLeaveRequestComponent extends BaseComponent implements OnChanges {
  @Input() selectedLeaveDetails!: LeaveRequestDetails | null;
  @Input() isLeaveApproval!: boolean | null;
  @Input() isFromView!: boolean;

  leaveTypes = LeaveType;
  leaveStatus = LeaveStatus;
  substituteLists!: Instructor[];
  rejectRequestForm!: FormGroup<LeaveApprovalForm>;

  @Output() closeViewSideNav = new EventEmitter<void>();
  @Output() refreshData = new EventEmitter<void>();

  constructor(private readonly leaveRequestService: LeaveRequestService, private readonly cdr: ChangeDetectorRef, private readonly localStorageService: LocalStorageService) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.currentUser = this.localStorageService.getItem(StorageItem.CurrentUser);
    if (changes['selectedLeaveDetails']?.currentValue) {
      this.selectedLeaveDetails = changes['selectedLeaveDetails']?.currentValue;
      this.initLeaveRequestForm();
      if (this.isLeaveApproval) {
        this.getSubstitutesList();
      }
      if (this.isLeaveApproval === false) {
        this.setRequiredBasedOnCondition('remark', true);
      }
    }
  }

  initLeaveRequestForm(): void {
    this.rejectRequestForm = new FormGroup<LeaveApprovalForm>({
      requestId: new FormControl(this.selectedLeaveDetails?.id, { nonNullable: true, validators: [Validators.required] }),
      remark: new FormControl(null, { nonNullable: true }),
      substituteDetailsId: new FormControl(undefined, { nonNullable: true }),
      status: new FormControl(this.isLeaveApproval ? this.leaveStatus.APPROVED : this.leaveStatus.REJECTED, {
        nonNullable: true,
        validators: [Validators.required]
      })
    });
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean): void {
    const control = this.rejectRequestForm.get(controlName);
    if (required) {
      control?.setValidators([Validators.required]);
    } else {
      control?.clearValidators();
    }
    control?.updateValueAndValidity();
  }

  getSkillBasedOnGrade(grade: number): string {
    const skillRange = this.constants.skillGradeRanges.find(range => grade >= range.minGrade && grade <= range.maxGrade);
    return skillRange ? skillRange.skillType : '';
  }

  get getSubstituteAvailabilityRequest(): SubstituteAvailabilityRequest {
    return {
      requestId: this.selectedLeaveDetails?.id,
      locationId: this.selectedLeaveDetails?.requesterAvailabilities?.map(item => item.locationId),
      instrumentDetails: this.selectedLeaveDetails?.requestInstruments?.map(item => ({
        instrumentId: item.id,
        isIntroductoryAvaLable: item.isIntroductoryClassAvailable,
        skillType: this.getSkillBasedOnGrade(item.gradeLevel)
      })),
      leaveStartDate: DateUtils.getUtcRangeForLocalDate(this.selectedLeaveDetails?.leaveStartDate ?? '').startUtc,
      leaveEndDate: DateUtils.getUtcRangeForLocalDate(this.selectedLeaveDetails?.leaveEndDate ?? '').endUtc,
      leaveStartTime: CommonUtils.combineDateAndTime(this.selectedLeaveDetails?.leaveStartDate ?? '', this.selectedLeaveDetails?.leaveStartTime ?? ''),
      leaveEndTime: CommonUtils.combineDateAndTime(this.selectedLeaveDetails?.leaveEndDate ?? '', this.selectedLeaveDetails?.leaveEndTime ?? ''),
      daysOfSchedule: this.getDaysOfSchedule(this.selectedLeaveDetails?.leaveStartDate, this.selectedLeaveDetails?.leaveEndDate)
    };
  }

  getDaysOfSchedule(start?: string, end?: string): number[] {
    const startDate = moment(start); 
    const endDate = moment(end);

    const weekdays: number[] = [];

    let currentDate = startDate.clone();

    while (currentDate.isSameOrBefore(endDate)) {
      weekdays.push(currentDate.isoWeekday());
      currentDate.add(1, 'day');
    }

    return [...new Set(weekdays)];
  }

  getSubstitutesList(): void {
    this.leaveRequestService
      .add(this.getSubstituteAvailabilityRequest, API_URL.leaveManagement.getAllSubstituteAvailability)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<Instructor[]>) => {
          this.substituteLists = res.result;
          this.cdr.detectChanges();
        }
      });
  }

  onRejectOrApproveLeave(): void {
    if (this.rejectRequestForm.invalid) {
      this.rejectRequestForm.markAllAsTouched();
      return;
    }
    this.rejectRequestForm.markAsUntouched();
    this.showBtnLoader = true;
    this.leaveRequestService
      .add(
        {
          ...this.rejectRequestForm.getRawValue()
        },
        API_URL.leaveManagement.leaveApproval
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.closeViewSideNav.emit();
          this.refreshData.emit();
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getLeaveFn(isLeaveApproval: boolean): void {
    this.isLeaveApproval = isLeaveApproval;
    if (isLeaveApproval) {
      this.getSubstitutesList();
    } else {
      this.setRequiredBasedOnCondition('remark', true);
    }
  }

  getDayInRange(startDate: string, scheduleDate: string): number {
    return moment(new Date(scheduleDate)).diff(new Date(startDate), 'days') + 1;
  }

  getHoursInRange(startTime: string, endTime: string): number {
    return CommonUtils.getHoursInRangeQuarter(startTime, endTime);
  }

  closeViewSideNavFun(): void {
    if ((this.isLeaveApproval === true || this.isLeaveApproval === false) && this.isFromView) {
      this.isLeaveApproval = null;
    } else {
      this.closeViewSideNav.emit();
    }
  }
}
