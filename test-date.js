const moment = require('moment');

// Test the current implementation
function toLocal(date, inputFormat = 'yyyy-MM-DDTHH:mm:ss.SSS+0000', outputFormat = 'yyyy-MM-DDTHH:mm:ss') {
  if (!date) return '';
  return moment.utc(date, inputFormat).local().format(outputFormat);
}

console.log('=== Testing Date Conversion ===');
console.log('Current timezone offset:', moment().utcOffset(), 'minutes');

// Test case 1: Your exact UTC time
const utcTime = '2025-11-12T18:29:00';
console.log('\nInput UTC time:', utcTime);

// Test with different input formats
console.log('\n--- Testing different input formats ---');
console.log('1. Default format (yyyy-MM-DDTHH:mm:ss.SSS+0000):', toLocal(utcTime));
console.log('2. Matching format (yyyy-MM-DDTHH:mm:ss):', toLocal(utcTime, 'yyyy-MM-DDTHH:mm:ss'));
console.log('3. No format specified:', moment.utc(utcTime).local().format('yyyy-MM-DDTHH:mm:ss'));

// Test with moment's standard formats
console.log('\n--- Testing with moment standard formats ---');
console.log('4. YYYY-MM-DDTHH:mm:ss format:', toLocal(utcTime, 'YYYY-MM-DDTHH:mm:ss', 'YYYY-MM-DDTHH:mm:ss'));
console.log('5. ISO format:', moment.utc(utcTime).local().format('YYYY-MM-DDTHH:mm:ss'));

// Debug moment parsing
console.log('\n--- Debug moment parsing ---');
console.log('moment.utc(utcTime) as UTC:', moment.utc(utcTime).format());
console.log('moment.utc(utcTime).local():', moment.utc(utcTime).local().format());
console.log('Expected IST time should be around 23:59 on Nov 12th');
