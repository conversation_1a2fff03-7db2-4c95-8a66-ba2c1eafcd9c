import { ChangeDetectorRef, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { DirectivesModule } from 'src/app/shared/directives/directives.module';
import { CBResponse, IdNameModel } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { NgxPaginationModule } from 'ngx-pagination';
import { CommonUtils } from 'src/app/shared/utils';
import { ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { UserBillingTransactionsResponse, PlanAndPassBillingFilters, BillStatus, BillingDetails } from '../../../../models';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { All } from 'src/app/pages/settings/pages/plan/models';
import { AuthService } from 'src/app/auth/services';
import { ActivatedRoute, Router } from '@angular/router';
import { AppToasterService } from 'src/app/shared/services';
import { MatSidenavModule } from '@angular/material/sidenav';
import { PaymentMethodsComponent } from 'src/app/shared/components/payment-methods/payment-methods.component';
import { PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { MatDatepickerModule } from '@angular/material/datepicker';
import moment from 'moment';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Account } from 'src/app/auth/models/user.model';
import { FilterPipe, LocalDatePipe } from 'src/app/shared/pipe';
import { MatIconModule } from '@angular/material/icon';
import { BillingSidenavComponent } from '../billing-sidenav/billing-sidenav.component';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    DirectivesModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    NgxPaginationModule,
    SharedModule,
    MatSelectModule,
    FormsModule,
    MatSidenavModule,
    MatDatepickerModule,
    MatTooltipModule,
    MatIconModule
  ],
  COMPONENTS: [PaymentMethodsComponent, BillingSidenavComponent],
  PIPES: [FilterPipe]
};

@Component({
  selector: 'app-open-bill',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  providers: [provideNativeDateAdapter()],
  templateUrl: './open-bill.component.html',
  styleUrl: './open-bill.component.scss'
})
export class OpenBillComponent extends BaseComponent implements OnChanges {
  @Input() currentUser$!: Account | null;
  @Input() studentList!: Array<IdNameModel>;

  openBillDetails!: Array<UserBillingTransactionsResponse>;
  totalCount!: number;
  selectedDependentId!: number;
  selectedUserId!: number;
  selectedBillDetail!: BillingDetails | null;
  selectedPaymentDetails!: PaymentParams | null;
  accManagerDetails!: Account | null;
  pageSize = this.paginationConfig.itemsPerPage;
  currentPage = this.paginationConfig.pageNumber;
  isBillingSideNavOpen = false;
  isDetailedBillingSideNavOpen = false;
  selectedDetailedBillingData!: BillingDetails | null;
  selectedDependentForBilling!: number | undefined;
  classType = ClassTypes;
  billStatus = BillStatus;
  all = All;
  searchTerm = '';

  filters: PlanAndPassBillingFilters = {
    statusFilter: BillStatus.OPEN,
    startDateFilter: moment().startOf('year').format(),
    endDateFilter: moment().add(1, 'week').format()
  };

  constructor(
    private readonly cdr: ChangeDetectorRef,
    protected readonly planSummaryService: PlanSummaryService,
    protected readonly schedulerService: SchedulerService,
    private readonly paymentService: PaymentService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['currentUser$']?.currentValue) {
      this.currentUser = changes['currentUser$'].currentValue;
      this.getCurrentId();
    }
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.getOpenBillDetails(this.currentPage, this.pageSize);
  }

  getAccountManagerDetails(openBillDetail: UserBillingTransactionsResponse): void {
    this.authService
      .getUserDetailsFromId(openBillDetail.userBillingTransactions.accountManagerId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.accManagerDetails = res.result;
          this.toggleSideNav(true, openBillDetail.userBillingTransactions, this.selectedPaymentDetails);
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getCurrentId(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params.activeTab === 'Open Bill') {
        if (params.dependentId) {
          this.selectedDependentId = +params.dependentId;
        } else {
          this.selectedDependentId = 0;
        }
        this.getOpenBillDetails((this.currentPage = 1), this.pageSize);
      }
    });
  }

  getFilterParams() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      RecurringBillStatus: this.filters.statusFilter,
      UserId: this.currentUser?.userRoleId === this.constants.roleIds.CLIENT ? this.currentUser?.userId : this.selectedUserId,
      dependentInformationId: this.selectedDependentId,
      CreatedStartDate: moment(this.filters.startDateFilter).format(this.constants.dateFormats.yyyy_MM_DD),
      CreatedEndDate: moment(this.filters.endDateFilter).format(this.constants.dateFormats.yyyy_MM_DD)
    });
  }

  getOpenBillDetails(currentPage: number, pageSize: number): void {
    if (!this.filters.endDateFilter) {
      return;
    }
    this.showPageLoader = true;
    this.paymentService
      .getListWithFiltersWithPagination<CBResponse<UserBillingTransactionsResponse>>(
        this.getFilterParams(),
        currentPage,
        pageSize,
        `${API_URL.payment.getAllBillingDetailsOfUser}`,
        false
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<UserBillingTransactionsResponse>) => {
          this.openBillDetails = res.result.items;
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  setStudentDetail(student: IdNameModel | null): void {
    this.selectedDependentId = 0;
    this.selectedUserId = student?.id ?? 0;
    this.getOpenBillDetails(this.currentPage, this.pageSize);
  }

  toggleSideNav(isOpen: boolean, billDetail: BillingDetails | null, selectedPaymentDetails: PaymentParams | null): void {
    this.isBillingSideNavOpen = isOpen;
    this.selectedBillDetail = billDetail;
    this.selectedPaymentDetails = selectedPaymentDetails;
  }

  toggleDetailedBillingSideNav(isOpen: boolean, billDetail: BillingDetails | null): void {
    this.isDetailedBillingSideNavOpen = isOpen;
    this.selectedDetailedBillingData = billDetail;
  }

  getDependentNames(billingDetails: BillingDetails): string[] {
    const uniqueNames = new Set<string>();
    billingDetails?.dependentBillingDetails.forEach(detail => uniqueNames.add(detail.dependentName));
    const names = Array.from(uniqueNames);
    return names;
  }

  openStudentDetailPage(dependentId: number): void {
    if (this.currentUser?.userRoleId === this.constants.roleIds.CLIENT) {
      return;
    }
    this.router.navigate([this.path.members.root, this.path.members.clients], {
      queryParams: { dependentId: dependentId }
    });
  }

  getPaymentParams() {
    return {
      dependentInformationId: this.selectedBillDetail?.dependentInformationId,
      billId: this.selectedBillDetail?.billId,
      id: this.selectedBillDetail?.id,
      paidDate: new Date(),
      customerVaultId: this.selectedPaymentDetails?.customerVaultId,
      transactionType: this.selectedPaymentDetails?.transactionType,
      paidAmount: this.selectedBillDetail?.billAmount
    };
  }

  onFailedBillPayment(): void {
    if (!this.selectedPaymentDetails?.customerVaultId) {
      this.toasterService.error(this.constants.errorMessages.noPaymentMethod);
      return;
    }
    this.showBtnLoader = true;
    this.paymentService
      .add(this.getPaymentParams(), API_URL.payment.singleBillPaymentUsingSavedCard)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.paymentSuccess);
          this.getOpenBillDetails(this.currentPage, this.pageSize);
          this.toggleSideNav(false, null, null);
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }
}
