import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';
import { NgxPaginationModule } from 'ngx-pagination';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { Debounce } from 'src/app/shared/decorators';
import { CBResponse } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CommonUtils } from 'src/app/shared/utils';
import { MatSelectModule } from '@angular/material/select';
import { DashIfEmptyPipe, EnumToKeyValuePipe } from 'src/app/shared/pipe';
import { IsSelf, LEAVE_YEAR_FORMATS, LeaveRequestDetails, LeaveRequestFilter, LeaveStatus } from '../../models';
import { LeaveRequestService } from '../../services';
import moment, { Moment } from 'moment';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ViewLeaveRequestComponent } from './pages/view-leave-request/view-leave-request.component';
import { All } from 'src/app/pages/settings/pages/plan/models';
import { ActivatedRoute } from '@angular/router';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MatDatepickerModule, MatDatepicker } from '@angular/material/datepicker';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { LocalStorageService, StorageItem } from 'src/app/shared/services/local-storage.service';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatInputModule,
    MatSidenavModule,
    NgxPaginationModule,
    CommonModule,
    SharedModule,
    MatSelectModule,
    MatTooltipModule,
    MatDatepickerModule
  ],
  COMPONENTS: [ViewLeaveRequestComponent],
  PIPES: [EnumToKeyValuePipe, DashIfEmptyPipe]
};

@Component({
  selector: 'app-leave-request-management',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    { provide: MAT_DATE_FORMATS, useValue: LEAVE_YEAR_FORMATS }
  ],
  templateUrl: './leave-request-management.component.html',
  styleUrl: './leave-request-management.component.scss'
})
export class LeaveRequestManagementComponent extends BaseComponent implements OnInit {
  @Input() isFromSupervisor!: boolean;

  totalCount!: number;
  leaveRequests!: Array<LeaveRequestDetails>;
  leaveStatus = LeaveStatus;
  all = All;

  pageSize = this.paginationConfig.itemsPerPage;
  currentPage = this.paginationConfig.pageNumber;
  isViewSideNavOpen = false;
  isLeaveDecisionSideNavOpen = false;
  isFromView = false;
  isLeaveApprovalOpen: boolean | null = null;
  selectedLeaveRequest!: LeaveRequestDetails | null;
  selectedYear = moment();
  sortColumn: string = '';
  sortDirection: 'asc' | 'desc' = 'asc';

  filters: LeaveRequestFilter = {
    searchTerm: null,
    statusFilter: this.all.ALL,
    isSelf: this.all.ALL,
    fromDateFilter: `${moment().year()}-01-01`,
    toDateFilter: `${moment().year()}-12-31`
  };

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly leaveRequestService: LeaveRequestService,
    private readonly localStorageService: LocalStorageService,
    private readonly activatedRoute: ActivatedRoute
  ) {
    super();
  }

  ngOnInit(): void {
    this.setActiveTabFromQueryParams();
    this.currentUser = this.localStorageService.getItem(StorageItem.CurrentUser);
    this.getLeaveRequests(this.currentPage, this.pageSize);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isFromSupervisor']?.currentValue) {
      this.isFromSupervisor = changes['isFromSupervisor'].currentValue;
    }
  }

  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (Object.keys(params).length) {
        if (Number(params.status) === this.leaveStatus.PENDING_ACTION) {
          this.filters.statusFilter = this.leaveStatus.PENDING_ACTION;
        }
      }
    });
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.getLeaveRequests(this.currentPage, this.pageSize);
  }

  onSort(column: string): void {
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortColumn = column;
      this.sortDirection = 'asc';
    }
    this.getLeaveRequests(this.currentPage, this.pageSize);
  }

  getFilterParams(currentPage: number, pageSize: number) {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      filter: this.filters.searchTerm,
      statusFilter: this.filters.statusFilter,
      Page: currentPage,
      PageSize: pageSize,
      IsSelf: IsSelf.FALSE,
      FromDateFilter: DateUtils.getUtcRangeForLocalDate(this.filters.fromDateFilter ?? '').startUtc,
      ToDateFilter: DateUtils.getUtcRangeForLocalDate(this.filters.toDateFilter ?? '').endUtc,
      SortBy: this.sortColumn,
      SortDirection: this.sortDirection
    });
  }

  getLeaveRequests(currentPage: number, pageSize: number): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();

    this.leaveRequestService
      .getListWithFilters<CBResponse<LeaveRequestDetails>>(
        this.getFilterParams(currentPage, pageSize),
        API_URL.leaveManagement.getLeaveRequest
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<LeaveRequestDetails>) => {
          this.leaveRequests = res.result.items.map(item => ({
            ...item,
            // Use default input format to handle both formats (with/without milliseconds and timezone)
            leaveStartDate: DateUtils.toLocal(item.leaveStartDate),
            leaveEndDate: DateUtils.toLocal(item.leaveEndDate),
            leaveStartTime: DateUtils.toLocal(item.leaveStartTime),
            leaveEndTime: DateUtils.toLocal(item.leaveEndTime),
            requestDate: DateUtils.toLocal(item.requestDate),
            approveDate: DateUtils.toLocal(item.approveDate)
          }));
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  chosenYearHandler(normalizedYear: any, datepicker: MatDatepicker<Moment>): void {
    const year = normalizedYear._d.getFullYear();
    this.selectedYear = normalizedYear.clone().startOf('year');
    this.filters.fromDateFilter = `${year}-01-01`;
    this.filters.toDateFilter = `${year}-12-31`;

    this.getLeaveRequests((this.currentPage = 1), this.pageSize);
    datepicker.close();
  }

  getDayInRange(startDate: string, scheduleDate: string): number {
    return moment(new Date(scheduleDate)).diff(new Date(startDate), 'days') + 1;
  }

  getHoursInRange(startTime: string, endTime: string): number {
    return CommonUtils.getHoursInRangeQuarter(startTime, endTime);
  }

  toggleSideNav(isOpen: boolean, leaveRequest: LeaveRequestDetails | null): void {
    this.isViewSideNavOpen = isOpen;
    this.selectedLeaveRequest = leaveRequest;
  }

  toggleLeaveDecisionSideNav(isLeaveApproval: boolean | null, isFromView: boolean): void {
    this.isLeaveApprovalOpen = isLeaveApproval;
    this.isFromView = isFromView;
  }

  @Debounce(300)
  onSearchTermChanged(): void {
    this.currentPage = 1;
    this.getLeaveRequests(this.currentPage, this.pageSize);
  }
}
