import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';
import { takeUntil } from 'rxjs';
import { AdvanceFiltersParams } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { AppToasterService } from 'src/app/shared/services';
import { SharedModule } from 'src/app/shared/shared.module';
import moment from 'moment';
import { AddLeaveRequestForm, IdEmail, LeaveBalance, LeaveDurationType, LeaveRequestDetails, LeaveType } from '../../../../models';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { EnumToKeyValuePipe } from 'src/app/shared/pipe';
import { LeaveRequestService } from '../../../../services';
import { CBGetResponse, IdNameModel } from 'src/app/shared/models';
import { MultiSelectChipsComponent } from '../../../../../../../../shared/components/multi-select-chips/multi-select-chips.component';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { minthirtyValidator, outOfRangeTimeValidator, timeRangeValidator } from 'src/app/shared/validators';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { CommonUtils } from 'src/app/shared/utils';
import { DateUtils } from 'src/app/shared/utils/date.utils';

const DEPENDENCIES = {
  MODULES: [
    SharedModule,
    MatSidenavModule,
    MatButtonModule,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatSelectModule,
    NgxMaterialTimepickerModule
  ],
  PIPES: [EnumToKeyValuePipe],
  COMPONENTS: [MultiSelectChipsComponent]
};

@Component({
  selector: 'app-add-leave-request',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './add-leave-request.component.html',
  styleUrl: './add-leave-request.component.scss'
})
export class AddLeaveRequestComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() leaveBalance!: LeaveBalance[] | null;
  @Input() selectedLeaveDetails!: LeaveRequestDetails | null;

  leaveRequestForm!: FormGroup<AddLeaveRequestForm>;
  peopleToNotifyList!: IdNameModel[];
  leaveTypes = LeaveType;
  leaveDurationType = LeaveDurationType;
  selectedDurationType = LeaveDurationType.FULL_DAY;
  maxDate = new Date();
  filters: AdvanceFiltersParams = {
    id: 1,
    defaultPlaceholder: 'Select people to notify',
    placeholder: 'Select people to notify',
    value: [] as Array<IdNameModel>,
    totalCount: 0,
    showMax: 1,
    isOpen: false,
    showSearchBar: true,
    options: [] as Array<IdNameModel>
  };

  @Output() refreshScheduleData = new EventEmitter<void>();
  @Output() closeSideNav = new EventEmitter<void>();

  constructor(
    private readonly toasterService: AppToasterService,
    private readonly leaveRequestService: LeaveRequestService,
    private readonly dialog: MatDialog,
    private readonly datePipe: DatePipe,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initLeaveRequestForm();
    this.getPeopleToNotifyList();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedLeaveDetails']?.currentValue) {
      this.selectedLeaveDetails = changes['selectedLeaveDetails']?.currentValue;
      if (this.selectedLeaveDetails) {
        // DEBUG: Test the smart detection
        console.log('=== DEBUGGING SMART DATE DETECTION ===');
        console.log('Original leaveStartDate:', this.selectedLeaveDetails?.leaveStartDate);
        console.log('Original leaveEndDate:', this.selectedLeaveDetails?.leaveEndDate);

        // Test UTC conversion (should convert)
        const testStart = '2025-11-11T18:30:00';
        const testEnd = '2025-11-12T18:29:00';
        console.log('UTC test start:', testStart, '→', DateUtils.toLocal(testStart));
        console.log('UTC test end:', testEnd, '→', DateUtils.toLocal(testEnd));

        // Test local detection (should NOT convert)
        const localStart = '2025-11-12T00:00:00';
        const localEnd = '2025-11-12T23:59:00';
        console.log('Local test start:', localStart, '→', DateUtils.toLocal(localStart));
        console.log('Local test end:', localEnd, '→', DateUtils.toLocal(localEnd));

        // Convert ALL fields as they are UTC timestamps
        this.selectedLeaveDetails.leaveStartTime = DateUtils.toLocal(this.selectedLeaveDetails?.leaveStartTime);
        this.selectedLeaveDetails.leaveEndTime = DateUtils.toLocal(this.selectedLeaveDetails?.leaveEndTime);
        this.selectedLeaveDetails.requestDate = DateUtils.toLocal(this.selectedLeaveDetails?.requestDate);
        this.selectedLeaveDetails.approveDate = DateUtils.toLocal(this.selectedLeaveDetails?.approveDate);
        this.selectedLeaveDetails.leaveStartDate = DateUtils.toLocal(this.selectedLeaveDetails?.leaveStartDate);
        this.selectedLeaveDetails.leaveEndDate = DateUtils.toLocal(this.selectedLeaveDetails?.leaveEndDate);

        console.log('Converted leaveStartDate:', this.selectedLeaveDetails.leaveStartDate);
        console.log('Converted leaveEndDate:', this.selectedLeaveDetails.leaveEndDate);
        console.log('=== END DEBUG ===');
      }
    }
  }

  initLeaveRequestForm(): void {
    this.leaveRequestForm = new FormGroup<AddLeaveRequestForm>({
      id: new FormControl(null, { nonNullable: true }),
      leaveType: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      requestDate: new FormControl('', { nonNullable: true }),
      leaveStartDate: new FormControl(null, { nonNullable: true, validators: [Validators.required] }),
      leaveEndDate: new FormControl(null, { nonNullable: true, validators: [Validators.required] }),
      reason: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      peopleToNotify: new FormControl([], { nonNullable: true }),
      leaveStartTime: new FormControl('', { nonNullable: true }),
      leaveEndTime: new FormControl('', { nonNullable: true })
    });
  }

  setLeaveForm(): void {
    if (this.selectedLeaveDetails) {
      this.leaveRequestForm?.patchValue({ ...this.selectedLeaveDetails });

      const idModelNameArray = this.peopleToNotifyList
        .filter(user => this.selectedLeaveDetails?.peopleToNotify.includes(user.name))
        .map(user => ({ id: user.id, name: user.name }));
      this.filters.value = idModelNameArray;

      this.onLeaveDurationTypeChange(this.selectedLeaveDetails?.leaveStartTime ? LeaveDurationType.CUSTOM : LeaveDurationType.FULL_DAY);
    }
  }

  onLeaveDurationTypeChange(type: LeaveDurationType): void {
    this.selectedDurationType = type;
    this.setRequiredBasedOnConditionForTime('leaveStartTime', type === LeaveDurationType.CUSTOM);
    this.setRequiredBasedOnConditionForTime('leaveEndTime', type === LeaveDurationType.CUSTOM);
    if (type === LeaveDurationType.FULL_DAY) {
      this.leaveRequestForm.controls.leaveStartTime.reset();
      this.leaveRequestForm.controls.leaveEndTime.reset();
    }
  }

  setRequiredBasedOnConditionForTime(controlName: string, required: boolean): void {
    const control = this.leaveRequestForm?.get(controlName);

    if (required) {
      control?.setValidators([Validators.required, outOfRangeTimeValidator()]);

      if (controlName === 'leaveStartTime') {
        this.leaveRequestForm.setValidators([
          timeRangeValidator('leaveStartTime', 'leaveEndTime'),
          minthirtyValidator('leaveStartTime', 'leaveEndTime')
        ]);
      }
    } else {
      control?.clearValidators();

      if (controlName === 'leaveStartTime') {
        this.leaveRequestForm.clearValidators();
      }
    }

    control?.updateValueAndValidity();
    this.leaveRequestForm.updateValueAndValidity();
  }

  setTime(control: string): void {
    const currentDate = this.datePipe.transform(
      this.leaveRequestForm.getRawValue().leaveStartDate || new Date(),
      this.constants.dateFormats.yyyy_MM_dd
    );
    const timeValue = this.datePipe.transform(
      new Date(`${currentDate} ${this.leaveRequestForm.get(control)?.value}`),
      this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
    );

    this.leaveRequestForm.get(control)?.setValue(timeValue ?? '');
  }

  setFormControlValue(controlName: string, value: number | string): void {
    (this.leaveRequestForm.controls as any)[controlName].setValue(value);
  }

  getDayInRange(): number {
    const startDate = this.leaveRequestForm.get('leaveStartDate')?.value;
    const endDate = this.leaveRequestForm.get('leaveEndDate')?.value;
    if (startDate && endDate) {
      return moment(new Date(endDate)).diff(new Date(startDate), 'days') + 1;
    }
    return 0;
  }

  getAllDays(startDate: string, endDate: string): number[] {
    return CommonUtils.getDaysOfWeekBetween(startDate, endDate);
  }

  getUnavailableDaysWithNoLeave(): boolean {
    const startDate = this.leaveRequestForm.get('leaveStartDate')?.value;
    const endDate = this.leaveRequestForm.get('leaveEndDate')?.value;
    if (!startDate || !endDate || !this.leaveBalance || !this.leaveBalance.length) {
      return false;
    }

    const daysInRange = this.getAllDays(startDate, endDate);
    const memberLeaves = this.leaveBalance[0].memberDayViseLeaves;
    const dayNames = this.constants.daysOfTheWeek.map(item => item.key);
    const unavailableDays: string[] = [];

    daysInRange.forEach(dayNum => {
      const leaveForDay = memberLeaves.find(item => item.day === dayNum);
      if (leaveForDay && leaveForDay.remainingLeaveDays <= 0 && this.leaveRequestForm.get('leaveType')?.value !== LeaveType.UNPAID) {
        unavailableDays.push(dayNames[dayNum]);
      }
    });

    if (unavailableDays.length) {
      this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Insufficient Leave Balance',
          message: `You have no leave balance for ${unavailableDays.join(', ')}. Please select different dates.`,
          acceptBtnName: 'OK',
          hideRejectBtn: true
        }
      });
      return true;
    }
    return false;
  }

  getPeopleToNotifyList(): void {
    this.showPageLoader = true;
    this.leaveRequestService
      .getList<CBGetResponse<IdEmail[]>>(API_URL.leaveManagement.getUserEmailDropDown)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<IdEmail[]>) => {
          this.peopleToNotifyList = res.result.map(item => ({ id: item.id, name: item.email }));
          this.filters.options = this.peopleToNotifyList;
          this.filters.totalCount = this.peopleToNotifyList.length;
          this.setLeaveForm();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getFormattedStartTime(): string {
    return this.datePipe.transform(this.selectedLeaveDetails?.leaveStartTime, 'shortTime') ?? '';
  }

  getFormattedEndTime(): string {
    return this.datePipe.transform(this.selectedLeaveDetails?.leaveEndTime, 'shortTime') ?? '';
  }

  onApplyLeave(): void {
    if (this.leaveRequestForm.invalid) {
      this.leaveRequestForm.markAllAsTouched();
      return;
    }
    if (this.getUnavailableDaysWithNoLeave()) {
      return;
    }
    this.leaveRequestForm.markAsUntouched();
    this.showBtnLoader = true;
    this.leaveRequestService
      .add(
        {
          ...this.leaveRequestForm.getRawValue(),
          requestDate: new Date(),
          leaveStartDate: DateUtils.getUtcRangeForLocalDate(this.leaveRequestForm.getRawValue().leaveStartDate ?? '').startUtc,
          leaveEndDate:
            this.selectedDurationType === LeaveDurationType.CUSTOM || this.leaveRequestForm.getRawValue().leaveStartDate === this.leaveRequestForm.getRawValue().leaveEndDate
              ? DateUtils.getUtcRangeForLocalDate(this.leaveRequestForm.getRawValue().leaveStartDate ?? '').endUtc
              : DateUtils.getUtcRangeForLocalDate(this.leaveRequestForm.getRawValue().leaveEndDate ?? '').endUtc,
          peopleToNotify: this.filters.value.map(item => item.name),
          leaveStartTime:
            this.selectedDurationType === LeaveDurationType.CUSTOM
              ? DateUtils.toUTC(this.leaveRequestForm.getRawValue().leaveStartTime ?? '', 'yyyy-MM-DDTHH:mm:ss')
              : null,
          leaveEndTime:
            this.selectedDurationType === LeaveDurationType.CUSTOM
              ? DateUtils.toUTC(this.leaveRequestForm.getRawValue().leaveEndTime ?? '', 'yyyy-MM-DDTHH:mm:ss')
              : null
        },
        API_URL.leaveManagement.createUpdate
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.onCloseSideNav();
          this.refreshScheduleData.emit();
          this.selectedLeaveDetails
            ? this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Leave Request'))
            : this.toasterService.success(this.constants.successMessages.addedSuccessfully.replace('{item}', 'Leave Request'));
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onCloseSideNav(): void {
    this.closeSideNav.emit();
    this.leaveRequestForm.reset();
  }
}
