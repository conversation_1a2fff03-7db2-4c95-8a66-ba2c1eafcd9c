@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.auth-page-with-header {
  overflow: hidden !important;
  height: calc(100vh - 104px) !important;

  .search-and-count-wrapper-auth {
    justify-content: space-between;

    .search-and-count-wrapper {
      @include flex-content-align-center;
    }
  }

  .visits-list {
    overflow: auto;
    height: calc(100vh - 220px);
    padding-right: 10px;

    .o-card {
      padding: 20px !important;

      .o-card-body {
        @include flex-content-space-between;

        .title {
          font-size: 17px;
          font-weight: 700;
          margin-bottom: 8px;

          &.admin-title {
            width: fit-content;
          }
        }

        .visit-content {
          @include flex-content-align-center;
          color: $gray-text;
          font-size: 15px;
          font-weight: 600;
          flex-wrap: wrap;

          .visit-info {
            display: flex;
          }
        }

        .visit-cancel-info {
          text-align: -webkit-right;
          font-weight: 700;
          font-size: 18px;

          .schedule-status {
            @include flex-content-center;
            width: fit-content;
            font-size: 14px;
            height: 33px;
            padding: 7px 10px;
            margin-top: 15px;
            border-radius: 4px;
            font-weight: 400;
            background-color: $header-schedule-bg-color;
            color: $primary-color;

            img {
              height: 16px;
              width: 16px;
              margin-right: 5px;
            }
          }
        }
      }
    }
  }
}

.pagination-controls {
  margin-top: 10px !important;
  margin-right: 10px;
}

.no-data-found-card {
  height: calc(100vh - 243px) !important;
}

:host ::ng-deep {
  .mat-mdc-text-field-wrapper {
    background-color: $white-color !important;
  }

  .mat-mdc-form-field-focus-overlay,
  .mat-mdc-text-field-wrapper,
  .mat-mdc-form-field-flex {
    height: 45px;
  }

  .mat-mdc-form-field-infix {
    min-height: 45px !important;
    padding: 10px 0 !important;
  }

  .mat-mdc-form-field {
    width: 250px !important;
  }
  .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }
}

@media (max-width: 767px) {
  .auth-page-with-header {
    .search-and-count-wrapper-auth {
      display: block;

      .search-and-count-wrapper {
        display: block !important;
        margin-bottom: 10px;

        .total-users {
          margin-bottom: 10px;

          span {
            border: none !important;
          }
        }
      }
    }

    .search-bar {
      .search-bar-wrapper {
        margin-left: 0px !important;
      }
    }

    .visits-list {
      height: calc(100vh - 328px) !important;

      .o-card {
        .o-card-body,
        .visit-content {
          flex-direction: column;
          flex-wrap: wrap;
          align-items: flex-start !important;

          .visit-info {
            margin-bottom: 6px;
          }

          &.location {
            flex-direction: row !important;
            align-items: center !important;
          }

          .dot {
            display: none;
          }

          .visit-cancel-info {
            text-align: left !important;
          }
        }
      }
    }
  }
}

@media (max-width: 867px) and (min-width: 766px) {
  .auth-page-with-header {
    .search-and-count-wrapper-auth {
      display: block;

      .search-and-count-wrapper {
        display: block !important;
        margin-bottom: 10px;

        .total-users {
          margin-bottom: 10px;

          span {
            border: none !important;
          }
        }
      }
    }

    .search-bar {
      .search-bar-wrapper {
        margin-left: 0px !important;
      }
    }
  }
}

.payment-mentod {
  @include flex-content-align-center;
  cursor: pointer;
  color: $primary-color;
  .mat-icon {
    color: $primary-color !important;
    margin-right: 2px;
  }
}

::ng-deep {
  .mat-drawer-content {
    overflow: hidden !important;
  }
  .sidenav-content-without-footer {
    overflow: hidden !important;
  }
  .mat-drawer-inner-container {
    overflow: hidden !important;
  }
}
