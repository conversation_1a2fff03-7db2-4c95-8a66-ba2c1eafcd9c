<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isViewSideNavOpen || isLeaveDecisionSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="md-sidebar"
    [disableClose]="true">
    @if (isViewSideNavOpen) {
        <app-view-leave-request [selectedLeaveDetails]="selectedLeaveRequest" [isLeaveApproval]="isLeaveApprovalOpen" [isFromView]="isFromView" (closeViewSideNav)="toggleSideNav(false, null); toggleLeaveDecisionSideNav(null, false)" (refreshData)="getLeaveRequests(currentPage, pageSize)"></app-view-leave-request>
    }
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="auth-page-wrapper auth-page-with-header">
      <div class="filter-and-count-wrapper">
        <div class="search-and-count-wrapper-auth">
          <div class="total-users">
            Total: <span>{{ totalCount }}</span>
          </div>
          <div class="search-bar">
            <mat-form-field class="search-bar-wrapper ms-3">
              <input
                matInput
                placeholder="Search.."
                [(ngModel)]="filters.searchTerm"
                (ngModelChange)="onSearchTermChanged()" />
              <mat-icon matTextPrefix>search</mat-icon>
            </mat-form-field>
          </div>
        </div>
        <div class="filter-wrapper">
          <mat-form-field class="search-bar-wrapper me-2">
          <input
              matInput
              [matDatepicker]="startPicker"
              [matTooltip]="filters.fromDateFilter + ' to ' + filters.toDateFilter"
              (click)="startPicker.open()"
              [(ngModel)]="selectedYear"
              placeholder="Select Year"
              readonly />
            <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
            <mat-datepicker #startPicker
              startView="multi-year"
              panelClass="year-only-picker"
              [startAt]="selectedYear"
              (yearSelected)="chosenYearHandler($event, startPicker)">
            </mat-datepicker>
          </mat-form-field>
          <mat-form-field class="search-bar-wrapper">
            <mat-select
              [(ngModel)]="filters.statusFilter"
              (selectionChange)="getLeaveRequests((currentPage = 1), pageSize)">
              <mat-option [value]="all.ALL">All Status</mat-option>
              <mat-option *ngFor="let leave of leaveStatus | enumToKeyValue" [value]="leave.value">
                {{ leave.key.replace('_',' ') | titlecase }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <div class="o-card" [ngClass]="{'is-from-supervisor': isFromSupervisor}">
        <div class="o-card-body">
          <div class="o-table">
            <div class="o-row o-header">
              <div class="o-cell first-cell">
                Request By
                <mat-icon class="sort-icon" (click)="onSort('requestDate')">
                  {{ sortColumn !== 'requestDate' ? 'unfold_more' : sortDirection === 'asc' ? 'arrow_upward' : 'arrow_downward' }}
                </mat-icon>                
              </div>
              <div class="o-cell text-start">
                Leave Date
                <mat-icon class="sort-icon" (click)="onSort('leaveStartDate')">
                  {{ sortColumn !== 'leaveStartDate' ? 'unfold_more' : sortDirection === 'asc' ? 'arrow_upward' : 'arrow_downward' }}
                </mat-icon>
              </div>
              <div class="o-cell">Leave Note</div>
              <div class="o-cell" *ngIf="filters.statusFilter === leaveStatus.APPROVED">Substitute</div>
              <div class="o-cell" *ngIf="filters.statusFilter === leaveStatus.REJECTED">Reject Reason</div>
              @if(currentUser?.userRole === constants.roles.ADMIN && filters.statusFilter === leaveStatus.PENDING_ACTION) {
                  <div class="o-cell">Action</div>
              }
              @else {
                  <div class="o-cell">Status</div>
              }
            </div>
            <div class="dotted-divider"></div> 
            <div class="content" [ngClass]="{
              'show-pagination': totalCount > 10, 
              'hide-pagination': totalCount <= 10,
              'is-from-supervisor-pagination': isFromSupervisor
            }">
              <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : leaveTemplate"></ng-container>
            </div>
          </div>
        </div>
      </div>
      @if (totalCount > 10) {
        <pagination-controls
          id="leave-management"
          [previousLabel]="''"
          [nextLabel]="''"
          (pageChange)="onPageChange($event)"
          [responsive]="true"
          class="pagination-controls"></pagination-controls>
      }
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #leaveTemplate>
  <ng-container [ngTemplateOutlet]="totalCount ? leaveTableContent : noDataFound"></ng-container>
</ng-template>

<ng-template #leaveTableContent>
  @for (
    leaveRequest of leaveRequests
      | paginate: { itemsPerPage: pageSize, currentPage: currentPage, totalItems: totalCount, id: "leave-management" };
    track $index
  ) {
    <div class="o-row">
      <div
        class="o-cell first-cell instructor-name-photo-wrapper" 
        (click)="toggleSideNav(true, leaveRequest); toggleLeaveDecisionSideNav(null, true)">
        <div class="student-name-wrapper">
          <div class="student-name">
            {{ leaveRequest.requestedBy | titlecase }}
          </div>
          <div class="text-gray">On {{ leaveRequest.requestDate | date }}</div>
        </div>
      </div>
      <div class="o-cell text-start">
        {{ leaveRequest.leaveStartDate | date }} - {{ leaveRequest.leaveEndDate | date }}
        <div class="text-gray"> {{ leaveRequest.leaveStartTime ? getHoursInRange(leaveRequest.leaveStartTime, leaveRequest.leaveEndTime) + ' hours' : getDayInRange(leaveRequest.leaveStartDate, leaveRequest.leaveEndDate) + ' day' }}</div>
      </div>
      <div class="o-cell">
        {{ leaveRequest.reason | titlecase | dashIfEmpty }}
      </div>
      <div class="o-cell" *ngIf="filters.statusFilter === leaveStatus.APPROVED">
        {{ leaveRequest.substituteName ?? 'No Substitute Available' | titlecase }}
      </div>
      <div class="o-cell" *ngIf="filters.statusFilter === leaveStatus.REJECTED">
        {{ leaveRequest.remark | titlecase | dashIfEmpty }}
      </div>
      <div class="o-cell d-flex justify-content-center align-items-center ms-2">
        @if (currentUser?.userRole === constants.roles.ADMIN && leaveRequest.status === leaveStatus.PENDING_ACTION) {
          <div class="icons">
            <img
              (click)="toggleSideNav(true, leaveRequest); toggleLeaveDecisionSideNav(true, false)"
              matTooltip="Approve" alt=""
              [src]="constants.staticImages.icons.checkCircle"
              class="approve me-3" />
            <img 
              (click)="toggleSideNav(true, leaveRequest); toggleLeaveDecisionSideNav(false, false)"
              matTooltip="Reject" alt=""
              [src]="constants.staticImages.icons.crossCircle"
              class="cancel" />
          </div>
        }
        @else {
          @switch (leaveRequest.status) {
              @case (leaveStatus.PENDING_ACTION) {
                  <div class="status onHold"><img [src]="constants.staticImages.icons.timeCircleClock"
                  class="me-1 yellow-filter" alt="" /> Pending</div>
              }
              @case (leaveStatus.APPROVED) {
                <div class="status enrolled"><img [src]="constants.staticImages.icons.checkCircle"
                  class="me-1" alt="" /> Approved</div>
              }
              @case (leaveStatus.REJECTED) {
                <div class="status canceled"><img [src]="constants.staticImages.icons.redCrossCircle" class="reject me-1" alt="" /> Rejected</div>
              }
            }
        }
      </div>
    </div>

    @if ($index < leaveRequests.length - 1) {
      <div class="dotted-divider"></div>
    }
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-wrapper">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
