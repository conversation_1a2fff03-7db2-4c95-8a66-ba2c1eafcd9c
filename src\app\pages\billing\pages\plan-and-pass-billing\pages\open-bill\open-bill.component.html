<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isBillingSideNavOpen || isDetailedBillingSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-750"
    [disableClose]="true">
    @if (isBillingSideNavOpen) {
        <div class="o-sidebar-wrapper">
            <div class="o-sidebar-header">
                <div class="title">Payment Methods</div>
                <div class="action-btn-wrapper">
                    <button mat-raised-button color="primary" class="mat-primary-btn back-btn" type="button" [appLoader]="showBtnLoader" (click)="onFailedBillPayment()">
                        Pay
                    </button>
                    <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="toggleSideNav(false, null, null)">
                        Close
                    </button>
                </div>
            </div>
            <div class="divider"></div>
            <div class="o-sidebar-body">
                <app-payment-methods [showDefaultPaymentBtn]="false" [accManagerDetails]="accManagerDetails" (sendSelectedPaymentDetail)="selectedPaymentDetails = $event" screen="billing-screen"></app-payment-methods>
            </div>
        </div>
    }
    @if (isDetailedBillingSideNavOpen) {
      <app-billing-sidenav
        [billingDetails]="selectedDetailedBillingData"
        [isOpenBill]="true"
        (closeSideNav)="toggleDetailedBillingSideNav(false, null)">
      </app-billing-sidenav>
    }
  </mat-sidenav>
</mat-sidenav-container>

<div class="auth-page-with-header">
  <div class="search-and-count-wrapper-auth">
    <div class="search-and-count-wrapper">
      <div class="total-users">
        Total: <span class="border-0">{{ totalCount }}</span>
      </div>
    </div> 
      <div class="filter-wrapper">
        <mat-form-field class="search-bar-wrapper" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
          <mat-select [(ngModel)]="selectedDependentId">
            <div class="search-bar mx-2">
              <mat-form-field class="search-bar-wrapper search-bar-wrapper-instructor">
                <input matInput placeholder="Search.." [(ngModel)]="searchTerm" (keydown.space)="$event.stopPropagation()" />
                <mat-icon matTextPrefix>search</mat-icon>
              </mat-form-field>
            </div>
            <mat-option [value]="all.ALL" (click)="setStudentDetail(null)">All Students</mat-option>
            <mat-option *ngFor="let student of studentList | filter : searchTerm" [value]="student.id" (click)="setStudentDetail(student)">
              {{ student.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field class="search-bar-wrapper">
          <mat-select
            [(ngModel)]="filters.statusFilter"
            (selectionChange)="getOpenBillDetails((currentPage = 1), pageSize)">
            <mat-option [value]="billStatus.OPEN">All</mat-option>
            <mat-option [value]="billStatus.FAILED">Failed</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field>
          <mat-date-range-input [rangePicker]="picker">
            <input
              matStartDate
              placeholder="Start date"
              [(ngModel)]="filters.startDateFilter"
              (click)="picker.open()" />
            <input
              matEndDate
              placeholder="End date"
              [(ngModel)]="filters.endDateFilter"
              (dateChange)="getOpenBillDetails((currentPage = 1), pageSize)"
              (click)="picker.open()" />
          </mat-date-range-input>
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-date-range-picker #picker></mat-date-range-picker>
        </mat-form-field>
      </div>
  </div>
  <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : billingLists"></ng-container>
</div>
<pagination-controls
  id="billing"
  [previousLabel]="''"
  [nextLabel]="''"
  (pageChange)="onPageChange($event)"
  [responsive]="true"
  class="pagination-controls">
</pagination-controls>

<ng-template #billingLists>
    <ng-container [ngTemplateOutlet]="openBillDetails && openBillDetails.length ? openBillList : noDataFound"></ng-container>
</ng-template>

<ng-template #openBillList>
  <div class="visits-list">
    @for (
      openBillDetail of openBillDetails
      | paginate: {
          itemsPerPage: pageSize,
          currentPage: currentPage,
          totalItems: totalCount,
          id: 'billing'
        };
      track $index
    ) {
      <div class="o-card mb-2">
        <div class="o-card-body pointer">
          <div (click)="toggleDetailedBillingSideNav(true, openBillDetail.userBillingTransactions)">
            <div class="title admin-title" matTooltip="Account Manager" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
              {{ openBillDetail.userBillingTransactions.accountManagerName | titlecase }}
            </div>
            <div class="title" *appHasPermission="[constants.roles.USER]">
              {{ openBillDetail.userBillingTransactions.billDate | date: constants.dateFormats.month }} Recurring Lesson Bill
            </div>

            <div class="visit-content mb-2" *appHasPermission="[constants.roles.USER]">
              <ng-container
                [ngTemplateOutlet]="billStatusTemp"
                [ngTemplateOutletContext]="{
                  openBillDetail: openBillDetail
                }"
              ></ng-container>
            </div>
            <div class="visit-content mb-2" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
              <div class="visit-info">
                <div class="me-1">Email</div>
                <div class="text-black">{{ openBillDetail.userBillingTransactions.accountManagerEmail }}</div>
              </div>
              <div class="dot"></div>
              <div class="visit-info">
                <div class="me-1">Phone No.</div>
                <div class="text-black">{{ openBillDetail.userBillingTransactions.accountManagerPhone }}</div>
              </div>
              <div class="dot"></div>
              <ng-container
                [ngTemplateOutlet]="billStatusTemp"
                [ngTemplateOutletContext]="{
                  openBillDetail: openBillDetail
                }"
              ></ng-container>
            </div>

            <div class="visit-content">
              <div class="visit-info">
                <div class="me-1">Payment Due Date:</div>
                <div class="text-red">
                  {{
                    openBillDetail.userBillingTransactions.billDate | date: 'mediumDate'
                  }}
                </div>
              </div>
              <div class="dot"></div>
              <div class="visit-info">
                <div class="me-1 text-black">Dependent</div>
                <div class="primary-color d-flex align-items-center">
                  {{ getDependentNames(openBillDetail.userBillingTransactions)[0] }}
                  @if (getDependentNames(openBillDetail.userBillingTransactions).length > 1) {
                    <div class="dot"></div>
                    <span [matTooltip]="getDependentNames(openBillDetail.userBillingTransactions).join(', ')"> +{{ getDependentNames(openBillDetail.userBillingTransactions).length - 1 }}</span>
                  }
                </div>
              </div>
              <div class="dot"></div>
              <div class="visit-info">
                <div class="primary-color me-1">
                  {{
                    schedulerService.getClassType(openBillDetail.userBillingTransactions.classType)
                  }}
                </div>
                <div>Lesson</div>
              </div>
            </div>
          </div>

          <div class="visit-cancel-info">
            <div>
              {{
                openBillDetail.userBillingTransactions.billAmount | currency: 'USD' : 'symbol' : '1.2-2'
              }}
            </div>
            <div class="action-buttons">
              <div *ngIf="openBillDetail.userBillingTransactions.billPaymentStatus === billStatus.FAILED">
                <button
                  mat-raised-button
                  color="primary"
                  class="mat-primary-btn mt-2"
                  type="button"
                  (click)="getAccountManagerDetails(openBillDetail)"
                  [appLoader]="showBtnLoader">
                  Pay Now
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    }
  </div>
</ng-template>

<ng-template #billStatusTemp let-openBillDetail="openBillDetail">
  <div class="visit-info">
    <div class="me-1">Status:</div>
    <div [ngClass]="{
        'text-yellow': openBillDetail.userBillingTransactions.billPaymentStatus === billStatus.OPEN,
        'text-red': openBillDetail.userBillingTransactions.billPaymentStatus === billStatus.FAILED
      }">
      {{
        billStatus[openBillDetail.userBillingTransactions.billPaymentStatus] | titlecase
      }}
    </div>
  </div>
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
