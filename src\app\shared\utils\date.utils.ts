import moment from 'moment';

export class DateUtils {
  static toUTC(date: string, outputFormat: 'YYYY/MM/DD' | 'hh:mm A' | 'YYYY/MM/DD hh:mm A' | 'yyyy-MM-DDTHH:mm:ss.SSS+0000' | 'yyyy-MM-DDTHH:mm:ss'): string {
    // Parse as local time and convert to UTC
    const normalizedOutputFormat = outputFormat.replace(/yyyy/g, 'YYYY').replace(/dd/g, 'DD');
    return moment(date).utc().format(normalizedOutputFormat);
  }

  static toLocal(
    date: string | undefined,
    inputFormat = 'yyyy-MM-DDTHH:mm:ss.SSS+0000',
    outputFormat = 'yyyy-MM-DDTHH:mm:ss'
  ): string {
    if (!date) {
      return '';
    }

    // Handle both lowercase and uppercase format strings for compatibility
    const normalizedInputFormat = inputFormat.replace(/yyyy/g, 'YYYY').replace(/dd/g, 'DD');
    const normalizedOutputFormat = outputFormat.replace(/yyyy/g, 'YYYY').replace(/dd/g, 'DD');

    // Smart detection: Check if the datetime is already in local format
    // If it looks like local datetime (e.g., 00:00:00 or 23:59:00), don't convert
    if (this.isAlreadyLocalDateTime(date)) {
      // Just format it without timezone conversion
      return moment(date).format(normalizedOutputFormat);
    }

    // Otherwise, treat as UTC and convert to local
    let utcMoment = moment.utc(date, normalizedInputFormat);

    // If the parsing with format didn't work well, try without format
    if (!utcMoment.isValid() || utcMoment.year() < 1900) {
      utcMoment = moment.utc(date);
    }

    return utcMoment.local().format(normalizedOutputFormat);
  }

  private static isAlreadyLocalDateTime(date: string): boolean {
    // Check if the time component suggests it's already local
    // Common patterns for local datetime: 00:00:00, 23:59:00, etc.
    const timePattern = /T(\d{2}):(\d{2}):(\d{2})/;
    const match = date.match(timePattern);

    if (match) {
      const hours = parseInt(match[1]);
      const minutes = parseInt(match[2]);
      const seconds = parseInt(match[3]);

      // If it's exactly midnight (00:00:00) or end of day (23:59:xx), likely local
      if ((hours === 0 && minutes === 0 && seconds === 0) ||
          (hours === 23 && minutes === 59)) {
        return true;
      }
    }

    return false;
  }

  static getDateTime(date: string, outputFormat: 'MM/DD/YYYY' | 'hh:mm A' | 'YYYY/MM/DD hh:mm A'): Date {
    return moment(date, outputFormat).toDate();
  }

  static getDateTimeString(date: string | undefined, outputFormat: 'MM/DD/YYYY' | 'hh:mm A' | 'YYYY/MM/DD hh:mm A'): string {
    if (!date) {
      return '';
    }
    return moment(date, outputFormat).toString();
  }

  static getUtcRangeForLocalDate(dateStr: Date | string): { startUtc: string; endUtc: string } {
    if(!dateStr) return { startUtc: '', endUtc: '' };
    const localDate = new Date(dateStr);

    const start = new Date(localDate.setHours(0, 0, 0, 0));
    const end = new Date(localDate.setHours(23, 59, 0, 0));

    return {
      startUtc: moment(start).utc().format('YYYY-MM-DDTHH:mm:ss'),
      endUtc: moment(end).utc().format('YYYY-MM-DDTHH:mm:ss')
    };
  }
}
