import moment from 'moment';

export class DateUtils {
  static toUTC(date: string, outputFormat: 'YYYY/MM/DD' | 'hh:mm A' | 'YYYY/MM/DD hh:mm A' | 'yyyy-MM-DDTHH:mm:ss.SSS+0000' | 'yyyy-MM-DDTHH:mm:ss'): string {
    // Parse as local time and convert to UTC
    const normalizedOutputFormat = outputFormat.replace(/yyyy/g, 'YYYY').replace(/dd/g, 'DD');
    return moment(date).utc().format(normalizedOutputFormat);
  }

  static toLocal(
    date: string | undefined,
    inputFormat = 'yyyy-MM-DDTHH:mm:ss.SSS+0000',
    outputFormat = 'yyyy-MM-DDTHH:mm:ss'
  ): string {
    if (!date) {
      return '';
    }
    // Parse as UTC and convert to local time
    // Handle both lowercase and uppercase format strings for compatibility
    const normalizedInputFormat = inputFormat.replace(/yyyy/g, 'YYYY').replace(/dd/g, 'DD');
    const normalizedOutputFormat = outputFormat.replace(/yyyy/g, 'YYYY').replace(/dd/g, 'DD');

    return moment.utc(date, normalizedInputFormat).local().format(normalizedOutputFormat);
  }

  static getDateTime(date: string, outputFormat: 'MM/DD/YYYY' | 'hh:mm A' | 'YYYY/MM/DD hh:mm A'): Date {
    return moment(date, outputFormat).toDate();
  }

  static getDateTimeString(date: string | undefined, outputFormat: 'MM/DD/YYYY' | 'hh:mm A' | 'YYYY/MM/DD hh:mm A'): string {
    if (!date) {
      return '';
    }
    return moment(date, outputFormat).toString();
  }

  static getUtcRangeForLocalDate(dateStr: Date | string): { startUtc: string; endUtc: string } {
    if(!dateStr) return { startUtc: '', endUtc: '' };
    const localDate = new Date(dateStr);

    const start = new Date(localDate.setHours(0, 0, 0, 0));
    const end = new Date(localDate.setHours(23, 59, 0, 0));

    return {
      startUtc: moment(start).utc().format('YYYY-MM-DDTHH:mm:ss'),
      endUtc: moment(end).utc().format('YYYY-MM-DDTHH:mm:ss')
    };
  }
}
