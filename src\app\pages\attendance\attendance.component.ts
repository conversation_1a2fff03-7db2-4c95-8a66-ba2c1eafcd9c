import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { CBGetResponse, CBResponse } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CommonUtils } from 'src/app/shared/utils';
import { MatSelectModule } from '@angular/material/select';
import { InstructorList } from 'src/app/schedule-introductory-lesson/models';
import { All } from 'src/app/pages/settings/pages/plan/models';

import {
  AdvancedFilters,
  ClassTypes,
  ScheduleDetail,
  ScheduleDetailsView
} from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { AuthService } from 'src/app/auth/services';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { CommonService } from 'src/app/shared/services';
import { Students } from '../members/pages/students/models';
import { SupervisorFilter } from '../members/pages/supervisors/models';
import { AttendanceFilters } from './models/attendance.model';
import { SchoolLocations } from '../room-and-location-management/models';
import { SchedulerService } from '../scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import moment from 'moment';
import { ViewAttendanceComponent } from './pages/view-attendance/view-attendance.component';
import { Debounce } from 'src/app/shared/decorators';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { DateUtils } from 'src/app/shared/utils/date.utils';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    MatIconModule,
    MatInputModule,
    MatSidenavModule,
    CommonModule,
    SharedModule,
    MatSelectModule,
    MatDatepickerModule
  ],
  COMPONENTS: [ViewAttendanceComponent],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-attendance',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  providers: [provideNativeDateAdapter()],
  templateUrl: './attendance.component.html',
  styleUrl: './attendance.component.scss'
})
export class AttendanceComponent extends BaseComponent implements OnInit {
  totalCount!: number;
  students!: Array<Students>;
  locations!: Array<SchoolLocations>;
  scheduleDetails!: Array<ScheduleDetailsView>;
  filteredScheduleDetails!: Array<ScheduleDetailsView>;
  all = All;
  classTypes = ClassTypes;

  isSideNavOpen = false;
  selectedScheduleId!: number | null;
  instructorIdUnderSupervisor!: Array<number>;
  appliedAdvanceFilter = new AdvancedFilters();

  filters: AttendanceFilters = {
    searchTerm: null,
    classType: 0,
    locationId: 0,
    instructorId: [],
    minScheduleDate: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd),
    maxScheduleDate: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd)
  };

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly instructorService: InstructorService,
    private readonly commonService: CommonService,
    private readonly authService: AuthService,
    private readonly datePipe: DatePipe,
    protected readonly schedulerService: SchedulerService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
    this.getLocations();
  }

  @Debounce(300)
  getSchedule(updateAttendance = true): void {
    if (!updateAttendance) {
      return;
    }

    this.showPageLoader = true;
    this.cdr.detectChanges();

    this.schedulerService
      .add(this.getFilterParams(this.filters.minScheduleDate!), API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<ScheduleDetail>) => {
          this.scheduleDetails = res.result.scheduleLessonDetails.filter((item) => !item.isDraftSchedule);
          this.onSearchTermChanged();
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParams(date: string) {
    const minScheduleDateFilter = DateUtils.getUtcRangeForLocalDate(date).startUtc;
    const maxScheduleDateFilter = DateUtils.getUtcRangeForLocalDate(date).endUtc;

    return {
      ...this.appliedAdvanceFilter,
      minScheduleDateFilter: minScheduleDateFilter,
      maxScheduleDateFilter: maxScheduleDateFilter,
      locationIdFilter: this.filters.locationId ? [this.filters.locationId] : [],
      instructorIdFilter: this.getInstructorFilter(),
      classTypeFilter: this.filters.classType ? [this.filters.classType] : [],
      instrumentIdFilter: []
    };
  }

  getInstructorFilter(): Array<number> {
    switch (this.currentUser?.userRoleId) {
      case this.constants.roleIds.INSTRUCTOR:
        return [this.currentUser?.dependentId];
      case this.constants.roleIds.SUPERVISOR:
        return [this.currentUser?.dependentId, ...this.filters.instructorId];
      default:
        return [];
    }
  }

  getFilterParamsForInstructors() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      SupervisorIdFilter: this.currentUser?.dependentId,
      isSupervisorFilter: SupervisorFilter.ALL,
      Page: 1
    });
  }

  getInstructorsWithSupervisor(): void {
    this.instructorService
      .add(this.getFilterParamsForInstructors(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<InstructorList>) => {
          this.filters.instructorId = res.result.items.map((item) => item.instructorDetail.id);
          this.getSchedule();
          this.cdr.detectChanges();
        }
      });
  }

  updateDay(daysToAdd: number): void {
    const date = moment(this.filters.minScheduleDate).add(daysToAdd, 'days');

    this.filters.minScheduleDate = date.format(this.constants.dateFormats.yyyy_MM_DD);
    this.filters.maxScheduleDate = date.format(this.constants.dateFormats.yyyy_MM_DD);
    this.getSchedule();
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.currentUser = res;
          if (this.currentUser?.isSupervisor) {
            this.getInstructorsWithSupervisor();
          } else {
            this.getSchedule();
          }
          this.cdr.detectChanges();
        }
      });
  }

  toggleSideNav(isOpen: boolean, scheduleDetailId: number | null): void {
    this.isSideNavOpen = isOpen;
    this.selectedScheduleId = scheduleDetailId;
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getInitials(firstName: string, lastName: string): string {
    return CommonUtils.getInitials(firstName, lastName);
  }

  getTimeDiff(start: string, end: string): number {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  onSearchTermChanged(): void {
    this.showPageLoader = true;

    setTimeout(() => {
      const lowerCaseSearchTerm = this.filters.searchTerm?.toLowerCase() || '';

      this.filteredScheduleDetails = this.scheduleDetails
        .filter((scheduleDetail) => {
          const nameToSearch =
            scheduleDetail.classType === this.classTypes.GROUP_CLASS
              ? scheduleDetail.groupClassName
              : scheduleDetail.classType === this.classTypes.SUMMER_CAMP
                ? scheduleDetail.campName :scheduleDetail.classType === this.classTypes.ENSEMBLE_CLASS
                ?scheduleDetail.ensembleClassName
                : scheduleDetail.instrumentName + ' Lesson';

          return nameToSearch?.toLowerCase().includes(lowerCaseSearchTerm);
        })
        .map((scheduleDetail) => ({
          ...scheduleDetail,
          isAllPresent: scheduleDetail.studentDetails.every((student) => student.isPresent !== null)
        }));

      this.totalCount = this.filteredScheduleDetails.length;
      this.showPageLoader = false;
      this.cdr.detectChanges();
    }, 1000);
  }
}
