@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.notes-detail-wrapper {

  .student-header-wrapper {
    @include flex-content-space-between;

    .student-header-content {
      @include flex-content-align-center;

      .main-title {
        font-size: 18px;
        font-weight: 700;
        margin-left: 10px;
      }

      img {
        height: 21px;
        width: 23px;
        filter: $black-filter;
      }
    }
  }

  .student-info-wrapper {
    margin-bottom: 5px;

    .note-img {
      cursor: pointer;
      height: 16px;
      width: 16px;
      filter: none !important;
    }

    img {
      height: 18px;
      width: 18px;
      filter: $primary-color-filter;
    }

    &.note-wrapper {
      overflow: auto;
      max-height: calc(100vh - 200px);
      padding-right: 10px;
      scrollbar-width: thin;
    }

    &.message {
      min-height: 70px;
    }
  }

  .chat-message {
    display: flex;

    .chat-image {
      .img,
      .placeholder-name {
        height: 45px;
        width: 45px;
        filter: none;
        border-radius: 5px;
      }
    }

    .chat-details {
      width: -webkit-fill-available;

      .chat-name {
        @include ellipse(300px);
      }

      .chat-header {
        @include flex-content-space-between;
      }

      .notes {
        @include ellipse(400px);
      }

      .date {
        font-size: 14px;
      }
    }
  }

  .no-schedule-available {
    @include flex-content-center;
    font-weight: 600;
    font-size: 22px;
    min-height: 100px;
  }

  .action-btn-wrapper {
    .back-btn {
      margin-right: 8px;
    }
  }

  .no-schedule-available {
    @include flex-content-center;
    font-weight: 500;
    font-size: 22px;
    min-height: 200px;
  }
}

::ng-deep {
  .page-loader-wrapper .content-loader {
    margin: 0px !important;
    height: 200px !important;
  }
}
