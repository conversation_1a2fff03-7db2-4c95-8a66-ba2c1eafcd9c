@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.o-card {
  background-color: $gray-bg-light;

  .card-header {
    @include flex-content-space-between;
    margin-bottom: 20px;

    .card-title {
      font-size: 18px;
      font-weight: 700;
      @include flex-content-align-center;

      mat-icon {
        margin-right: 10px;
        color: $primary-color;
        font-size: 22px;
      }
    }
  }

  .existing-plans-wrapper {
    overflow: auto;
    max-height: calc(100vh - 435px);

    .existing-plans {
      font-weight: 700;

      .detail-value {
        font-size: 16px;
        margin-left: 10px;
      }

      .detail-info {
        @include flex-content-align-center;
        color: $gray-text;
        font-size: 14px;
        margin-left: 10px;
        margin-top: 3px;
      }

      .plan-amount {
        color: $primary-color;
        font-size: 16px;
      }
    }
  }

  .clickable {
    color: $primary-color !important;
    text-decoration: underline;
    cursor: pointer;
  }

  .billing-breakdown {
    .billing-item {
      @include flex-content-space-between;
      padding-bottom: 15px;
      border-bottom: 1px solid $gray-bg-light;

      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
      }

      &.discount {
        .billing-value {
          color: $primary-color;
          font-weight: 700;
        }
      }

      &.total {
        padding-top: 15px;
        font-weight: 700;
        font-size: 16px;

        .billing-value {
          color: $primary-color;
        }
      }

      .billing-label {
        color: $gray-text;
        font-weight: 700;
      }

      .billing-value {
        font-size: 16px;
        font-weight: 700;
        color: $original-black-color;
      }
    }
  }
}

.o-sidebar-body {
  padding: 20px 30px !important;
  overflow: auto;
  height: calc(100vh - 56px);

  .section-header {
    @include flex-content-align-center;
    font-weight: 700;
    font-size: 18px;
    margin-bottom: 16px;
    gap: 8px;

    .material-icons {
      font-size: 20px;
    }
  }

  .payment-note {
    @include flex-content-align-center;
    padding: 10px;
    background-color: $gray-bg-light;
    border-radius: 8px;

    mat-icon {
      margin-right: 10px;
      font-size: 18px;
    }

    span {
      color: $gray-text;
      font-weight: 600;

      .clickable {
        color: $primary-color;
        cursor: pointer;
        text-decoration: underline;
        font-weight: 600;

        &:hover {
          color: darken($primary-color, 10%);
        }
      }
    }
  }

  .plan-details-section {
    margin-bottom: 24px;

    .plan-details-card {
      background-color: $gray-bg-light;
      border-radius: 10px;
      padding: 20px;
      font-weight: 700;

      .plan-detail-row {
        @include flex-content-space-between;
        margin-bottom: 12px;
        align-items: flex-start;

        &:last-child {
          margin-bottom: 0;
        }

        .plan-meta {
          color: $gray-text;
          font-weight: 400;
          font-size: 13px;

          .dot {
            display: inline-block;
          }
        }

        .plan-detail-label {
          min-width: 120px;
        }

        .plan-detail-value {
          text-align: right;
          flex: 1;

          &.plan-amount {
            color: $primary-color;
          }
        }
      }
    }
  }

  .existing-plans-section {
    margin-bottom: 24px;

    .existing-plans-list {
      background-color: $gray-bg-light;
      border-radius: 10px;
      padding: 20px;

      .existing-plan-wrapper {
        overflow: auto;
        max-height: calc(100vh - 435px);

        .existing-plan-item {
          @include flex-content-space-between;
          align-items: flex-start;
          margin-bottom: 16px;
          gap: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .plan-number {
            font-weight: 700;
          }

          .plan-info {
            flex: 1;

            .plan-name {
              font-weight: 700;
              font-size: 16px;
              color: $original-black-color;
            }

            .plan-meta {
              @include flex-content-align-center;
              color: $gray-text;
              font-weight: 600;
              font-size: 14px;
              gap: 8px;
            }
          }

          .plan-amount {
            font-weight: 700;
            color: $primary-color;
            text-align: right;
          }
        }
      }
    }
  }
}

::ng-deep {
  .mat-drawer-inner-container {
    overflow: hidden !important;
  }
  .sidenav-content-without-footer {
    overflow: hidden !important;
  }
}

@media (max-width: 767px) {
  .accordion-item {
    flex-wrap: wrap;

    .plan-content {
      flex-wrap: wrap;

      .dot {
        display: none;
      }
    }

    .plan-pricing {
      margin-top: 10px;
      width: 100%;
      text-align: left !important;
    }
  }
}
