<div class="notes-detail-wrapper">
    <div class="student-header-wrapper">
        <div class="student-header-content">
            <img [src]="constants.staticImages.icons.message" alt="" />
            <div class="main-title">Notes</div>
        </div>
        <div class="primary-color pointer" *ngIf="!isAddNoteOpen" (click)="toggleAddNote(true, null)">+ Add Note</div>
    </div>
    <ng-container [ngTemplateOutlet]="isAddNoteOpen ? addNoteTemp : showNotesTemp"></ng-container>
</div>

<ng-template #addNoteTemp>
    <mat-form-field class="mt-3">
      <textarea matInput placeholder="Add a note..." [(ngModel)]="notes" cols="12" rows="3"></textarea>
    </mat-form-field>
    <div class="action-btn-wrapper text-end">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="toggleAddNote(false, null)">Cancel</button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn ms-2"
        type="button"
        (click)="onSaveNote()"
        [appLoader]="showBtnLoader"
      >
        Save
      </button>
    </div>
</ng-template>

<ng-template #showNotesTemp>
  @if (studentNotes && studentNotes.length) {
    <div class="student-info-wrapper mt-3 message note-wrapper">
      <ng-container [ngTemplateOutlet]="showNotesLoader ? showLoader : notesTemp"></ng-container>
    </div>
    } @else {
    <div class="no-schedule-available">No Notes Available!</div>
  }
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>

<ng-template #notesTemp>
  @for (note of studentNotes; track $index) {
    <div class="chat-message" [ngClass]="{'mb-3': !$last}">
      <div class="chat-image">
        @if (note.profilePictureUrl) {
          <img [src]="note.profilePictureUrl" class="img me-3" alt="" />
          } @else {
          <div class="placeholder-name">
            <div>
              {{ getInitialsFromFullName(note.updatedUserName) | uppercase }}
            </div>
          </div>
          }
      </div>
      <div class="chat-details d-flex justify-content-between align-items-center">
        <div>
          <div class="chat-header">
            <div class="chat-message-text">
              <div class="flex-wrap">{{ note.notes }}</div>
            </div>
          </div>
          <div class="text-gray date">{{ note.updatedTime | localDate | date: constants.fullDate }} at {{ note.updatedTime | localDate | date: 'shortTime' }} <span class="primary-color">{{ note.updatedUserName }}</span></div>
        </div>
        <div class="d-flex align-items-center" *ngIf="currentUser?.userRoleId === constants.roleIds.ADMIN || currentUser?.userRoleId === constants.roleIds.DESK_MANAGER || note.updatedUserId === currentUser?.userId">
          <img class="note-img me-2" [src]="constants.staticImages.icons.editPenGreen" alt="" matTooltip="Edit Note" (click)="toggleAddNote(true, note)" />
          <img class="note-img" [src]="constants.staticImages.icons.redTrash" alt="" matTooltip="Delete Note" (click)="onDeleteNoteConfirmation(note.id)" />
        </div>
      </div>
    </div>
  }
</ng-template>