import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { DependentBillingDetail, BillingDetails, RefundTransactionDetails, RefundStatus, BillStatus } from 'src/app/pages/billing/models';
import { ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { TransactionTypes } from 'src/app/pages/shop/models';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { Router } from '@angular/router';
import { LocalStorageService, StorageItem } from 'src/app/shared/services/local-storage.service';
import { SharedModule } from 'src/app/shared/shared.module';
import { LocalDatePipe } from 'src/app/shared/pipe';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, MatIconModule, SharedModule],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-billing-sidenav',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  templateUrl: './billing-sidenav.component.html',
  styleUrl: './billing-sidenav.component.scss'
})
export class BillingSidenavComponent extends BaseComponent implements OnChanges {
  @Input() billingDetails!: BillingDetails | null;
  @Input() isOpenBill!: boolean;

  classTypes = ClassTypes;
  transactionTypes = TransactionTypes;
  totalDiscount = 0;
  isDiscountApplied = false;
  billStatus = BillStatus;
  currentPlanDetail: DependentBillingDetail | undefined;
  otherPlanDetails: DependentBillingDetail[] = [];
  dependentDetails!: string[];
  refundDetail!: RefundTransactionDetails | undefined;

  @Output() closeSideNav = new EventEmitter<void>();

  constructor(
    protected readonly schedulerService: SchedulerService,
    protected readonly planSummaryService: PlanSummaryService,
    private readonly router: Router,
    private readonly localStorageService: LocalStorageService
  ) {
    super();
  }

  ngOnInit(): void {
    this.currentUser = this.localStorageService.getItem(StorageItem.CurrentUser);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['billingDetails']?.currentValue || changes['dependentId']?.currentValue) {
      this.getDependentNames();
      this.getTotalDiscount();
      if (!this.isOpenBill) {
        this.processBillingDetails();
        this.findSuccessfulRefundTransaction();
      }
    }
  }

  processBillingDetails(): void {
    if (!this.billingDetails) {
      this.currentPlanDetail = undefined;
      this.otherPlanDetails = [];
      return;
    }

    if (this.billingDetails.dependentBillingDetails && this.billingDetails.dependentBillingDetails.length) {
      this.currentPlanDetail = this.billingDetails.dependentBillingDetails.find(detail => detail.planId === this.billingDetails?.planId);

      this.otherPlanDetails = this.billingDetails.dependentBillingDetails.filter(detail => detail.planId !== this.billingDetails?.planId);
    }
  }

  findSuccessfulRefundTransaction(): void {
    this.refundDetail = this.billingDetails?.refundTransactionDetails.find(detail => detail.transactionStatus === RefundStatus.SUCCESS);
  }

  openStudentDetailPage(dependentId: number): void {
    if (this.currentUser?.userRoleId === this.constants.roleIds.CLIENT || this.isOpenBill) {
      return;
    }
    this.router.navigate([this.path.members.root, this.path.members.clients], {
      queryParams: { dependentId: dependentId }
    });
  }

  getDependentNames(): void {
    const uniqueNames = new Set<string>();
    this.billingDetails?.dependentBillingDetails.forEach(detail => uniqueNames.add(detail.dependentName));
    const names = Array.from(uniqueNames);
    this.dependentDetails = names;
  }

  getTotalDiscount(): void {
    this.totalDiscount = 0;
    this.isDiscountApplied = false;
    if (this.billingDetails?.dependentBillingDetails.some(detail => detail.discountedAmount > 0)) {
      this.isDiscountApplied = true;
    }
    if (this.isDiscountApplied) {
      this.totalDiscount = this.billingDetails?.dependentBillingDetails.reduce((a, b) => a + b.discountedAmount, 0) ?? 0;
    }
  }

  closeSideNavFun(): void {
    this.closeSideNav.emit();
  }
}
