import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../shared.module';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { AllAchOfUser, CardDetailsResponse, PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { AuthService } from 'src/app/auth/services';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';
import { takeUntil } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { Account, Address } from 'src/app/auth/models/user.model';
import { AppToasterService } from '../../services';
import { MatRadioModule } from '@angular/material/radio';
import { BaseComponent } from '../base-component/base.component';
import { AchMethodComponent } from '../ach-method/ach-method.component';

const DEPENDENCIES = {
  MODULES: [MatButtonModule, SharedModule, FormsModule, CommonModule, MatCheckboxModule, MatIconModule, MatRadioModule],
  COMPONENTS: [AchMethodComponent]
};

@Component({
  selector: 'app-ach-details',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, AchMethodComponent],
  templateUrl: './ach-details.component.html',
  styleUrls: ['./ach-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AchDetailsComponent extends BaseComponent implements OnInit {
  @Input() screen!: string;
  @Input() accManagerDetails!: Account | null;
  @Input() achDetails!: AllAchOfUser[];
  savedAddressDetails!: Address;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() refreshAchDetails = new EventEmitter<void>();

  constructor(
    private readonly paymentService: PaymentService,
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getAddress();
    this.cdr.detectChanges();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.showPageLoader = true;
    if (changes['achDetails']?.currentValue) {
      this.achDetails = changes['achDetails'].currentValue;
    }
    if (changes['accManagerDetails']?.currentValue) {
      this.accManagerDetails = changes['accManagerDetails'].currentValue;
    }
    this.showPageLoader = false;
    this.cdr.detectChanges();
  }

  getAddress(): void {
    this.savedAddressDetails = {
      firstName: this.accManagerDetails?.firstName ?? '',
      lastName: this.accManagerDetails?.lastName ?? '',
      address: this.accManagerDetails?.address ?? '',
      city: this.accManagerDetails?.city ?? '',
      state: this.accManagerDetails?.state ?? '',
      zipCode: this.accManagerDetails?.zipCode ?? ''
    };
  }

  savedCardPayment(): void {
    this.showBtnLoader = true;
    this.paymentService.setUserPayment(this.getCardDetailsUsingSavedCard(this.achDetails[0]));
  }

  getCardDetailsUsingSavedCard(card: PaymentParams): CardDetailsResponse {
    return {
      isUsingSavedCard: true,
      customerVaultId: card.customerVaultId
    };
  }

  deleteAchMethod(ach: AllAchOfUser): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete ACH Information`,
        message: `Are you sure you want to delete this ACH information?`
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result.isConfirmed) {
        this.deleteAchDetails(ach);
        this.cdr.detectChanges();
      }
    });
  }

  deleteAchDetails(ach: AllAchOfUser): void {
    this.showPageLoader = true;
    this.paymentService
      .deleteCard(ach.customerVaultId as string, this.accManagerDetails?.userId as number)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'ACH details'));
          this.refreshAchDetails.emit();
          this.cdr.detectChanges();
        }
      });
  }

  closeSideNavFn(): void {
    this.closeSideNav.emit();
  }
}
