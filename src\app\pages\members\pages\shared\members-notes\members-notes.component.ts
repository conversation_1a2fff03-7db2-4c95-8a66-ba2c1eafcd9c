import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { takeUntil } from 'rxjs';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBResponse, MatDialogRes } from 'src/app/shared/models';
import { LocalDatePipe } from 'src/app/shared/pipe';

import { SharedModule } from 'src/app/shared/shared.module';
import { CommonUtils } from 'src/app/shared/utils';
import { StudentNotes } from '../../students/models';
import { StudentNotesService } from '../../students/services';
import { InstructorNotes } from '../../instructors/models';
import { InstructorNotesService } from '../../instructors/services/instructorNotesService.service';
import { LocalStorageService, StorageItem } from 'src/app/shared/services/local-storage.service';
import { Account } from 'src/app/auth/models/user.model';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule, MatFormFieldModule, MatInputModule, MatTooltipModule, FormsModule],
  PIPES: [LocalDatePipe],
  COMPONENTS: []
};

@Component({
  selector: 'app-members-notes',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './members-notes.component.html',
  styleUrl: './members-notes.component.scss'
})
export class MembersNotesComponent extends BaseComponent implements OnChanges {
  @Input() studentId!: number;
  @Input() instructorId!: number;

  isAddNoteOpen = false;
  showNotesLoader = false;
  studentNotes: Array<StudentNotes> | Array<InstructorNotes> = [];
  notes = '';
  selectedNoteId: number | undefined;

  @Output() refreshNotes = new EventEmitter<number>();

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly studentNotesService: StudentNotesService,
    private readonly instructorNotesService: InstructorNotesService,
    private readonly localStorageService: LocalStorageService,
    private readonly dialog: MatDialog
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['studentId']?.currentValue) {
      this.studentId = changes['studentId'].currentValue;
      this.getStudentNotes(this.studentId);
      this.cdr.detectChanges();
    }
    if (changes['instructorId']?.currentValue) {
      this.instructorId = changes['instructorId'].currentValue;
      this.getInstructorNotes(this.instructorId);
      this.cdr.detectChanges();
    }
  }

  ngOnInit(): void {
    this.currentUser = this.localStorageService.getItem(StorageItem.CurrentUser) as Account;
  }

  getStudentNotes(studentId: number): void {
    this.showNotesLoader = true;
    this.studentNotesService
      .getList<CBResponse<StudentNotes>>(`${API_URL.studentNotes.getAllNotes}?dependentInformationId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentNotes>) => {
          this.studentNotes = res.result.items;
          this.showNotesLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showNotesLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstructorNotes(instructorId: number): void {
    this.showNotesLoader = true;
    this.instructorNotesService
      .getList<CBResponse<InstructorNotes>>(`${API_URL.instructorNotes.getAllNotes}?instructorId=${instructorId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<InstructorNotes>) => {
          this.studentNotes = res.result.items;
          this.showNotesLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showNotesLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  toggleAddNote(isOpen: boolean, note: StudentNotes | InstructorNotes | null): void {
    this.isAddNoteOpen = isOpen;
    this.notes = note?.notes || '';
    this.selectedNoteId = note?.id;
  }

  onSaveNote(): void {
    if (!this.notes) {
      return;
    }
    if (this.instructorId) {
      this.saveInstructorNote();
    } else {
      this.saveStudentNote();
    }
  }

  saveStudentNote(): void {
    this.showBtnLoader = true;
    this.studentNotesService
      .add(
        {
          id: this.selectedNoteId,
          dependentInformationId: this.studentId,
          notes: this.notes
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.getStudentNotes(this.studentId);
          this.toggleAddNote(false, null);
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  saveInstructorNote(): void {
    this.showBtnLoader = true;
    this.instructorNotesService
      .add(
        {
          id: this.selectedNoteId,
          instructorId: this.instructorId,
          notes: this.notes
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.getInstructorNotes(this.instructorId);
          this.toggleAddNote(false, null);
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onDeleteNoteConfirmation(noteId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Delete Note',
        message: 'Are you sure you want to delete this note?'
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.onDeleteNote(noteId);
      }
    });
  }

  onDeleteNote(noteId: number): void {
    if (this.instructorId) {
      this.deleteInstructorNote(noteId);
    } else {
      this.deleteStudentNote(noteId);
    }
  }

  deleteStudentNote(noteId: number): void {
    this.studentNotesService
      .delete(noteId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.getStudentNotes(this.studentId);
          this.cdr.detectChanges();
        }
      });
  }

  deleteInstructorNote(noteId: number): void {
    this.instructorNotesService
      .delete(noteId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.getInstructorNotes(this.instructorId);
          this.cdr.detectChanges();
        }
      });
  }

  getInitialsFromFullName(name: string): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }
}
