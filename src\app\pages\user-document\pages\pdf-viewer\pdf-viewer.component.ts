import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SignedDocumentsInfo } from '../../models';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { SafePipe } from 'src/app/shared/pipe';
import { SignedDocumentService } from '../../services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { AppToasterService } from 'src/app/shared/services';
import {
  AllAchOfUser,
  AllCardsOfUser,
  AllCustomerCards,
  PaymentParams
} from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { AssignedPlanStatus, ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { PlanRecurringPaymentComponent } from '../plan-recurring-payment/plan-recurring-payment.component';
import { TransactionTypes } from 'src/app/pages/shop/models';
import { AuthService } from 'src/app/auth/services';
import { CBGetResponse } from 'src/app/shared/models';
const DEPENDENCIES = {
  MODULES: [MatButtonModule, SharedModule, CommonModule, MatCheckboxModule, FormsModule],
  PIPES: [SafePipe],
  COMPONENTS: [PlanRecurringPaymentComponent]
};

@Component({
  selector: 'app-pdf-viewer',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './pdf-viewer.component.html',
  styleUrl: './pdf-viewer.component.scss'
})
export class PdfViewerComponent extends BaseComponent implements OnInit, OnChanges, OnDestroy {
  @Input() documentInfo!: SignedDocumentsInfo | null;
  @Input() selectedPlanDocument!: string | null;
  @Input() isFromAdmin!: boolean;
  @Input() isPlanRenewal!: boolean;

  isAgreementDone = false;
  showPaymentTemplate = false;
  isRePayment = false;
  isAgreementSubmitted = false;
  defaultCardDetail: AllCardsOfUser | null = null;
  defaultAchDetail: AllAchOfUser | null = null;
  totalProcessingFee = 0;
  firstPaymentProcessingFee = 0;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() refereshDocuments = new EventEmitter<void>();
  @Output() shrinkMatSideNav = new EventEmitter<void>();

  constructor(
    private readonly signedDocumentService: SignedDocumentService,
    private readonly cdr: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly paymentService: PaymentService,
    private readonly authService: AuthService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
    if (this.documentInfo?.isAgreementDone && this.isPlanRenewal) {
      this.isAgreementDone = true;
      this.isAgreementSubmitted = true;
      this.showPaymentTemplate = false;
      return;
    }
    if (this.documentInfo?.isAgreementDone && !this.documentInfo?.isPaid && !this.isPlanRenewal && !this.isFromAdmin) {
      this.showPaymentTemplate = true;
      this.isAgreementSubmitted = true;
      this.showPageLoader = false;
      this.shrinkMatSideNav.emit();
    }
    if (this.showPaymentTemplate) {
      this.shrinkMatSideNav.emit();
    }
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getUserDetailsFromId(this.documentInfo?.accountManagerId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res.result;
          this.getAllCustomerCards();
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  ngOnChanges(): void {
    this.showPageLoader = true;
  }

  onDocumentLoaded(): void {
    this.showPageLoader = false;
  }

  onPayClick(): void {
    if (this.showPaymentTemplate) {
      this.initPaymentProcess();
    } else {
      this.onAcceptTermsAndCondition();
    }
  }

  onAcceptTermsAndCondition(): void {
    this.showBtnLoader = true;
    this.signedDocumentService
      .add(
        {
          id: this.documentInfo?.id,
          studentId: this.documentInfo?.studentId,
          documentId: this.documentInfo?.documentId,
          filePath: this.documentInfo?.filePath,
          uploadDate: this.documentInfo?.uploadDate,
          isAgreementDone: this.isAgreementDone,
          updatedPlanStatus: this.isPlanRenewal ? AssignedPlanStatus.PAYMENT_DONE : null,
          agreementDate: new Date()
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.markedSuccessfully.replace('{item}', 'Agreement'));
          if (this.isPlanRenewal) {
            this.showPaymentTemplate = false;
            this.createRecurringSubscription();
          } else {
            this.showPaymentTemplate = true;
            this.refereshDocuments.emit();
          }
          this.shrinkMatSideNav.emit();
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getAllCustomerCards(): void {
    this.showPageLoader = false;
    if (!this.currentUser?.userId) {
      return;
    }

    this.paymentService
      .getList<CBGetResponse<AllCustomerCards>>(`${API_URL.payment.getAllCustomerCards}?userId=${this.currentUser?.userId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<AllCustomerCards>) => {
          if (res.result) {
            this.defaultCardDetail = res.result.getAllCardsOfUser?.find(card => card.isDefault) || null;
            this.defaultAchDetail = res.result.getAllAchDetailsOfUser?.find(ach => ach.isDefault) || null;

            if (this.defaultCardDetail) {
              this.getProcessingFeeForCard();
            }
          }
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getProcessingFeeForCard(): void {
    this.totalProcessingFee = +((this.documentInfo?.planAmount ?? 0) * 0.035).toFixed(2);

    if (this.documentInfo?.serviceFees && this.documentInfo?.registrationFees) {
      this.firstPaymentProcessingFee = +(
        (this.documentInfo?.firstPayment + this.documentInfo?.serviceFees + this.documentInfo?.registrationFees) *
        0.035
      );
    }
  }

  initPaymentProcess(): void {
    this.showBtnLoader = true;

    if (this.defaultAchDetail || this.defaultCardDetail) {
      this.onConfirmationForPayment();
    } else {
      this.showBtnLoader = false;
      this.toasterService.error(this.constants.errorMessages.noPaymentMethod);
      this.cdr.detectChanges();
    }
  }

  get getPaymentDetails(): PaymentParams {
    const amount = this.defaultCardDetail ? this.getTotalAmount + this.getTotalAmount * 0.035 : this.getTotalAmount;
    return {
      dependentInformationId: this.documentInfo?.studentId,
      classType: this.documentInfo?.classType ?? ClassTypes.RECURRING,
      scheduleId: this.documentInfo?.recurringScheduleId ?? 0,
      paidDate: new Date(),
      customerVaultId: this.defaultAchDetail?.customerVaultId ?? this.defaultCardDetail?.customerVaultId,
      transactionType: this.defaultCardDetail ? TransactionTypes.CARD : TransactionTypes.ACH,
      paidAmount: amount - this.documentInfo?.discountedAmount!,
      totalAmount: amount,
      discountedAmount: this.documentInfo?.discountedAmount,
      planId: this.documentInfo?.planId
    };
  }

  get getTotalAmount(): number {
    if (!this.documentInfo) {
      return 0;
    }
    return this.documentInfo.firstPayment + this.documentInfo?.serviceFees + this.documentInfo?.registrationFees;
  }

  onConfirmationForPayment(): void {
    this.paymentService
      .add(this.getPaymentDetails, API_URL.payment.paymentUsingSavedCard)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.createRecurringSubscription();
          this.showBtnLoader = false;
          this.toasterService.success(this.constants.successMessages.paymentSuccess);
          this.cdr.detectChanges();
        },
        error: () => {
          this.isRePayment = true;
          this.isAgreementSubmitted = false;
          this.showBtnLoader = false;
          this.shrinkMatSideNav.emit();
          this.toasterService.error(this.constants.errorMessages.paymentFailed);
          this.cdr.detectChanges();
        }
      });
  }

  get getRecurringPaymentParams() {
    const baseAmount = this.documentInfo?.planAmount ?? 0;
    const planAmount = this.defaultCardDetail ? baseAmount + baseAmount * 0.035 : baseAmount;

    return {
      userId: this.currentUser?.userId,
      customerVaultId: this.defaultAchDetail?.customerVaultId ?? this.defaultCardDetail?.customerVaultId,
      planAmount: planAmount,
      startDate: this.documentInfo?.planStartDate,
      studentPlanId: this.documentInfo?.planId,
    };
  }

  createRecurringSubscription(): void {
    this.showBtnLoader = true;
    this.paymentService
      .add(this.getRecurringPaymentParams, API_URL.payment.createRecurringSubscription)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.refereshDocuments.emit();
          this.closeSideNavFun();
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  closeSideNavFun(): void {
    this.closeSideNav.emit();
  }
}
