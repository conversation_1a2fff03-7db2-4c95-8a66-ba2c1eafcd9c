import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { CBGetResponse, CBResponse, MatDialogRes } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { CommonUtils } from 'src/app/shared/utils';
import { All } from 'src/app/pages/settings/pages/plan/models';
import moment, { Moment } from 'moment';
import { IsSelf, LEAVE_YEAR_FORMATS, LeaveBalance, LeaveRequestDetails, LeaveRequestFilter, LeaveStatus, LeaveType } from '../../models';
import { LeaveRequestService } from '../../services';
import { EChartsOption } from 'echarts';
import { AddLeaveRequestComponent } from './pages/add-leave-request/add-leave-request.component';
import { DashIfEmptyPipe } from 'src/app/shared/pipe';
import { ViewLeaveHistoryComponent } from './pages/view-leave-history/view-leave-history.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { ActivatedRoute } from '@angular/router';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { MatDatepicker, MatDatepickerModule } from '@angular/material/datepicker';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DateUtils } from 'src/app/shared/utils/date.utils';

const DEPENDENCIES = {
  MODULES: [
    SharedModule,
    MatSidenavModule,
    MatSelectModule,
    MatButtonModule,
    CommonModule,
    FormsModule,
    MatInputModule,
    MatIconModule,
    MatDatepickerModule,
    MatTooltipModule
  ],
  COMPONENTS: [AddLeaveRequestComponent, ViewLeaveHistoryComponent],
  PIPES: [DashIfEmptyPipe]
};

@Component({
  selector: 'app-leave-dashboard',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    { provide: MAT_DATE_FORMATS, useValue: LEAVE_YEAR_FORMATS }
  ],
  templateUrl: './leave-dashboard.component.html',
  styleUrl: './leave-dashboard.component.scss'
})
export class LeaveDashboardComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() startDate!: string;
  @Input() endDate!: string;
  @Input() isFromSupervisor!: boolean;
  @Input() isRequestLeaveSideNavOpen!: boolean;

  totalLeaves!: number;
  usedLeaves!: number;
  availableLeaves!: number;
  leaveRequests!: Array<LeaveRequestDetails>;
  all = All;
  leaveType = LeaveType;
  leaveStatus = LeaveStatus;
  filters: LeaveRequestFilter = {
    searchTerm: null,
    statusFilter: this.all.ALL,
    isSelf: this.all.ALL,
    fromDateFilter: this.startDate,
    toDateFilter: this.endDate
  };
  chartOptions!: EChartsOption;
  isViewSideNavOpen = false;
  selectedLeaveDetails!: LeaveRequestDetails | null;
  maxDate = moment();
  selectedYear = moment();
  leaveBalance!: LeaveBalance[];

  @Output() isRequestLeaveSideNavOpenOutput = new EventEmitter<boolean>();

  constructor(
    private readonly leaveRequestService: LeaveRequestService,
    private readonly cdr: ChangeDetectorRef,
    private readonly activatedRoute: ActivatedRoute,
    private readonly dialog: MatDialog
  ) {
    super();
  }

  ngOnInit(): void {
    this.getLeaveBalance();
    this.getLeaveRequests();
    this.setActiveTabFromQueryParams();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.isRequestLeaveSideNavOpen = changes['isRequestLeaveSideNavOpen']?.currentValue ?? this.isRequestLeaveSideNavOpen;
  }

  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (Object.keys(params).length) {
        if (params.mode === 'add') {
          this.toggleLeaveRequest(true, null);
        }
        return;
      }
    });
  }

  getFilterParams() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      statusFilter: this.filters.statusFilter,
      FromDateFilter: DateUtils.getUtcRangeForLocalDate(this.filters.fromDateFilter ?? '').startUtc,
      ToDateFilter: DateUtils.getUtcRangeForLocalDate(this.filters.toDateFilter ?? '').endUtc,
      IsSelf: IsSelf.TRUE,
      page: 1
    });
  }

  getLeaveRequests(): void {
    this.showPageLoader = true;
    this.leaveRequestService
      .getListWithFilters<CBResponse<LeaveRequestDetails>>(this.getFilterParams(), API_URL.leaveManagement.getLeaveRequest)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<LeaveRequestDetails>) => {
          this.leaveRequests = res.result.items.map(item => ({
            ...item,
            // Use default input format to handle both formats (with/without milliseconds and timezone)
            leaveStartDate: DateUtils.toLocal(item.leaveStartDate),
            leaveEndDate: DateUtils.toLocal(item.leaveEndDate),
            leaveStartTime: DateUtils.toLocal(item.leaveStartTime),
            leaveEndTime: DateUtils.toLocal(item.leaveEndTime),
            requestDate: DateUtils.toLocal(item.requestDate),
            approveDate: DateUtils.toLocal(item.approveDate)
          }));
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getLeaveBalance(): void {
    this.leaveRequestService
      .getList<CBGetResponse<LeaveBalance[]>>(API_URL.leaveManagement.getLeaveBalance)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<LeaveBalance[]>) => {
          this.leaveBalance = res.result;
          this.totalLeaves = this.leaveBalance[0].totalLeaveDays;
          this.usedLeaves = this.leaveBalance[0].usedLeaveDays;
          this.availableLeaves = this.leaveBalance[0].remainingLeaveDays;
          this.chartOptions = CommonUtils.getChartOptions(this.availableLeaves, this.usedLeaves, this.totalLeaves);
          this.cdr.detectChanges();
        }
      });
  }

  getPendingLeaveRequests(): LeaveRequestDetails[] {
    return this.leaveRequests?.filter(request => request.status === this.leaveStatus.PENDING_ACTION) ?? [];
  }

  getOtherLeaveRequests(): LeaveRequestDetails[] {
    return (
      this.leaveRequests?.filter(request => request.status === this.leaveStatus.REJECTED || request.status === this.leaveStatus.APPROVED) ??
      []
    );
  }

  toggleLeaveRequest(isOpen: boolean, leaveRequest: LeaveRequestDetails | null): void {
    this.isRequestLeaveSideNavOpen = isOpen;
    this.isRequestLeaveSideNavOpenOutput.emit(isOpen);
    this.selectedLeaveDetails = leaveRequest;
  }

  toggleViewSideNav(isOpen: boolean, leaveRequest: LeaveRequestDetails | null): void {
    this.isViewSideNavOpen = isOpen;
    this.selectedLeaveDetails = leaveRequest;
  }

  deleteLeaveRequestConfirmation(leaveRequestId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Leave Request`,
        message: `Are you sure you want to delete this leave request?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.deleteLeaveRequest(leaveRequestId);
      }
    });
  }

  deleteLeaveRequest(leaveRequestId: number): void {
    this.leaveRequestService
      .delete(leaveRequestId, API_URL.leaveManagement.deleteLeaveRequest)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.getLeaveRequests();
          this.cdr.detectChanges();
        }
      });
  }

  chosenYearHandler(normalizedYear: any, datepicker: MatDatepicker<Moment>): void {
    const year = normalizedYear._d.getFullYear();
    this.selectedYear = normalizedYear.clone().startOf('year');
    this.filters.fromDateFilter = `${year}-01-01`;
    this.filters.toDateFilter = `${year}-12-31`;
    this.getLeaveRequests();
    datepicker.close();
  }

  getDayInRange(startDate: string, scheduleDate: string): number {
    return moment(new Date(scheduleDate)).diff(new Date(startDate), 'days') + 1;
  }

  getHoursInRange(startTime: string, endTime: string): number {
    return CommonUtils.getHoursInRangeQuarter(startTime, endTime);
  }
}
